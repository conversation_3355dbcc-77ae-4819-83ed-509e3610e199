#!/bin/bash

# Video Initialization Failure Test Script
# 视频初始化失败测试脚本

set -e

echo "=== Video Initialization Failure Test ==="
echo "Testing video initialization failure scenarios"

# 获取脚本目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "${SCRIPT_DIR}/../.." && pwd)"
BIN_DIR="${ROOT_DIR}/build/bin"
ORIGINAL_TOOL="${BIN_DIR}/multi_sensor_tool"
FIXED_TOOL="${BIN_DIR}/multi_sensor_tool_fixed"
TEST_LOG_DIR="${ROOT_DIR}/test/output/video_init_logs"

# 创建日志目录
mkdir -p "${TEST_LOG_DIR}"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建测试配置文件
create_video_test_configs() {
    local config_dir="${ROOT_DIR}/test/configs"
    
    # 1. 缺失视频文件测试
    cat > "${config_dir}/video_missing_file_test.txt" << 'EOF'
[missing-video-file]
# 缺失的视频文件
video:nonexistent_video.ts,nonexistent_timestamps.txt,rtsp://127.0.0.1:8554/test1

[missing-timestamp-file]
# 缺失的时间戳文件
video:test/data/sample.ts,nonexistent_timestamps.txt,rtsp://127.0.0.1:8554/test2
EOF

    # 2. 无效RTSP地址测试
    cat > "${config_dir}/video_invalid_rtsp_test.txt" << 'EOF'
[invalid-rtsp-url]
# 无效的RTSP地址
video:test/data/sample.ts,test/data/timestamps.txt,rtsp://invalid_host:99999/test

[invalid-rtsp-format]
# 无效的RTSP格式
video:test/data/sample.ts,test/data/timestamps.txt,invalid_rtsp_url
EOF

    # 3. 网络连接失败测试
    cat > "${config_dir}/video_network_fail_test.txt" << 'EOF'
[network-connection-fail]
# 网络连接失败 - 使用不可达的地址
video:test/data/sample.ts,test/data/timestamps.txt,rtsp://***************:8554/test

[port-in-use]
# 端口被占用测试
video:test/data/sample.ts,test/data/timestamps.txt,rtsp://127.0.0.1:22/test
EOF

    log_info "Created video test configuration files"
}

# 创建测试数据文件
create_test_data() {
    local data_dir="${ROOT_DIR}/test/data"
    mkdir -p "${data_dir}"
    
    # 创建一个简单的时间戳文件
    cat > "${data_dir}/timestamps.txt" << 'EOF'
1723169047000
1723169047040
1723169047080
1723169047120
1723169047160
EOF

    # 创建一个空的TS文件（模拟损坏的视频文件）
    touch "${data_dir}/sample.ts"
    
    # 创建一个包含无效数据的TS文件
    echo "invalid video data" > "${data_dir}/invalid_sample.ts"
    
    log_info "Created test data files"
}

# 测试视频初始化失败场景
test_video_init_failure() {
    local tool_name="$1"
    local tool_path="$2"
    local test_name="$3"
    local config_file="$4"
    local config_group="$5"
    
    log_info "Testing ${test_name} with ${tool_name}"
    
    local log_file="${TEST_LOG_DIR}/${tool_name}_${test_name}.log"
    local result="UNKNOWN"
    
    # 运行测试，期望失败
    timeout 10s "${tool_path}" -f "${config_file}" -g "${config_group}" -d > "${log_file}" 2>&1 &
    local test_pid=$!
    
    # 等待测试完成或超时
    if wait $test_pid 2>/dev/null; then
        local exit_code=$?
        if [ $exit_code -eq 0 ]; then
            result="UNEXPECTED_SUCCESS"
            log_warning "${tool_name} - ${test_name}: Unexpected success (should have failed)"
        else
            result="EXPECTED_FAILURE"
            log_success "${tool_name} - ${test_name}: Expected failure (exit code: ${exit_code})"
        fi
    else
        # 超时或被杀死
        kill $test_pid 2>/dev/null || true
        result="TIMEOUT"
        log_warning "${tool_name} - ${test_name}: Timeout"
    fi
    
    # 检查日志中的错误信息
    if grep -q "Failed to initialize video stream" "${log_file}"; then
        log_info "  ✓ Found expected error message: 'Failed to initialize video stream'"
    elif grep -q "Cannot open input file" "${log_file}"; then
        log_info "  ✓ Found expected error message: 'Cannot open input file'"
    elif grep -q "Cannot read timestamp file" "${log_file}"; then
        log_info "  ✓ Found expected error message: 'Cannot read timestamp file'"
    elif grep -q "Cannot create output context" "${log_file}"; then
        log_info "  ✓ Found expected error message: 'Cannot create output context'"
    else
        log_warning "  ? No specific video initialization error found in log"
    fi
    
    return 0
}

# 运行所有视频初始化失败测试
run_all_video_tests() {
    local tool_name="$1"
    local tool_path="$2"
    local config_dir="${ROOT_DIR}/test/configs"
    
    log_info "=== Testing ${tool_name} Video Initialization Failures ==="
    
    # 测试用例定义
    local tests=(
        "missing_video_file ${config_dir}/video_missing_file_test.txt missing-video-file"
        "missing_timestamp_file ${config_dir}/video_missing_file_test.txt missing-timestamp-file"
        "invalid_rtsp_url ${config_dir}/video_invalid_rtsp_test.txt invalid-rtsp-url"
        "invalid_rtsp_format ${config_dir}/video_invalid_rtsp_test.txt invalid-rtsp-format"
        "network_connection_fail ${config_dir}/video_network_fail_test.txt network-connection-fail"
        "port_in_use ${config_dir}/video_network_fail_test.txt port-in-use"
    )
    
    for test_def in "${tests[@]}"; do
        read -r test_name config_file config_group <<< "$test_def"
        test_video_init_failure "${tool_name}" "${tool_path}" "${test_name}" "${config_file}" "${config_group}"
        sleep 1
    done
}

# 比较两个版本的错误处理
compare_error_handling() {
    log_info "=== Comparing Error Handling Between Versions ==="
    
    # 分析日志文件中的错误信息
    local original_logs="${TEST_LOG_DIR}/original_*.log"
    local fixed_logs="${TEST_LOG_DIR}/fixed_*.log"
    
    log_info "Original version error patterns:"
    for log_file in ${original_logs}; do
        if [ -f "$log_file" ]; then
            local basename=$(basename "$log_file")
            local error_count=$(grep -c "ERROR\|Failed\|Cannot" "$log_file" 2>/dev/null || echo "0")
            log_info "  ${basename}: ${error_count} error messages"
        fi
    done
    
    log_info "Fixed version error patterns:"
    for log_file in ${fixed_logs}; do
        if [ -f "$log_file" ]; then
            local basename=$(basename "$log_file")
            local error_count=$(grep -c "ERROR\|Failed\|Cannot" "$log_file" 2>/dev/null || echo "0")
            log_info "  ${basename}: ${error_count} error messages"
        fi
    done
}

# 清理函数
cleanup() {
    log_info "Cleaning up..."
    
    # 杀死可能残留的测试进程
    pkill -f multi_sensor_tool 2>/dev/null || true
    
    log_success "Cleanup completed"
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 主执行流程
main() {
    log_info "Starting video initialization failure tests..."
    
    # 检查工具是否存在
    if [ ! -f "${ORIGINAL_TOOL}" ] || [ ! -f "${FIXED_TOOL}" ]; then
        log_error "Tools not found. Please build them first."
        exit 1
    fi
    
    # 创建测试配置和数据
    create_video_test_configs
    create_test_data
    
    # 测试原版工具
    run_all_video_tests "original" "${ORIGINAL_TOOL}"
    
    # 等待一下
    sleep 2
    
    # 测试修复版工具
    run_all_video_tests "fixed" "${FIXED_TOOL}"
    
    # 比较错误处理
    compare_error_handling
    
    log_success "Video initialization failure tests completed!"
    log_info "Detailed logs available in: ${TEST_LOG_DIR}"
    
    return 0
}

# 运行主函数
main "$@"
