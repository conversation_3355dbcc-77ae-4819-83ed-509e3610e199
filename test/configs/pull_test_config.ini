# RTSP拉流测试配置文件

[streams]
stream1=rtsp://127.0.0.1:8554/stream1
stream2=rtsp://127.0.0.1:8554/stream2
stream3=rtsp://127.0.0.1:8554/stream3
stream4=rtsp://127.0.0.1:8554/stream4

[sei_parsing]
vendor_type=auto              # auto, hikvision, dahua, generic
debug_mode=true               # 启用SEI解析调试信息
fallback_parsing=true         # SEI解析失败时尝试其他格式

[output]
console_update_interval=1000  # 控制台更新间隔(ms)
csv_output=test/output/pull_test_results.csv      # CSV输出文件
log_file=test/output/pull_test.log        # 详细日志文件
statistics_interval=10        # 统计报告间隔(秒)

[network]
connection_timeout=5000       # 连接超时(ms)
read_timeout=10000           # 读取超时(ms)
max_retries=3                # 最大重试次数
retry_delay=1000             # 重试延迟(ms)

[analysis]
delay_warning_threshold=100   # 延迟警告阈值(ms)
timestamp_continuity_check=true  # 检查时间戳连续性
frame_drop_detection=true     # 检测丢帧
