#!/bin/bash

# RTSP拉流工具测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
BUILD_DIR="$PROJECT_ROOT/build"
TEST_OUTPUT_DIR="$PROJECT_ROOT/test/output"

echo -e "${BLUE}=== RTSP拉流工具测试脚本 ===${NC}"
echo "项目根目录: $PROJECT_ROOT"

# 创建必要的目录
mkdir -p "$TEST_OUTPUT_DIR"
mkdir -p "$BUILD_DIR/bin"

# 检查FFmpeg依赖
check_ffmpeg() {
    echo -e "${YELLOW}检查FFmpeg依赖...${NC}"
    if ! pkg-config --exists libavformat libavcodec libavutil; then
        echo -e "${RED}错误：未找到FFmpeg开发库${NC}"
        echo "请安装FFmpeg开发包："
        echo "  Ubuntu/Debian: sudo apt-get install libavformat-dev libavcodec-dev libavutil-dev"
        echo "  CentOS/RHEL: sudo yum install ffmpeg-devel"
        exit 1
    fi
    echo -e "${GREEN}FFmpeg依赖检查通过${NC}"
}

# 编译拉流工具
build_puller() {
    echo -e "${YELLOW}编译拉流工具...${NC}"
    cd "$PROJECT_ROOT/src/rtsp_puller"
    
    # 检查依赖
    make check-deps
    
    # 清理旧文件
    make clean
    
    # 编译
    make -j$(nproc)
    
    # 安装到build目录
    make install
    
    echo -e "${GREEN}拉流工具编译完成${NC}"
}

# 编译推流工具（如果需要）
build_pusher() {
    echo -e "${YELLOW}检查推流工具...${NC}"
    
    if [ ! -f "$BUILD_DIR/bin/rtsp_tool" ]; then
        echo -e "${YELLOW}编译推流工具...${NC}"
        cd "$PROJECT_ROOT"
        make clean
        make -j$(nproc)
        echo -e "${GREEN}推流工具编译完成${NC}"
    else
        echo -e "${GREEN}推流工具已存在${NC}"
    fi
}

# 启动推流工具（后台运行）
start_pusher() {
    echo -e "${YELLOW}启动推流工具...${NC}"
    
    # 检查是否有旧进程
    if pgrep -f "rtsp_tool" > /dev/null; then
        echo -e "${YELLOW}停止旧的推流进程...${NC}"
        pkill -f "rtsp_tool" || true
        sleep 2
    fi
    
    # 启动推流工具（海康威视格式，循环播放，调试模式）
    cd "$PROJECT_ROOT"
    nohup "$BUILD_DIR/bin/rtsp_tool" \
        -f "test/configs/push_test_config.ini" \
        -g "test_group" \
        -l -d -t 2 \
        > "$TEST_OUTPUT_DIR/push_tool.log" 2>&1 &
    
    PUSH_PID=$!
    echo "推流工具PID: $PUSH_PID"
    echo $PUSH_PID > "$TEST_OUTPUT_DIR/push_tool.pid"
    
    # 等待推流工具启动
    echo -e "${YELLOW}等待推流工具启动...${NC}"
    sleep 5
    
    # 检查推流工具是否正常运行
    if ! ps -p $PUSH_PID > /dev/null; then
        echo -e "${RED}推流工具启动失败${NC}"
        cat "$TEST_OUTPUT_DIR/push_tool.log"
        exit 1
    fi
    
    echo -e "${GREEN}推流工具启动成功${NC}"
}

# 运行拉流测试
run_pull_test() {
    echo -e "${YELLOW}启动拉流测试...${NC}"
    
    cd "$PROJECT_ROOT"
    
    # 运行拉流工具（测试60秒）
    "$BUILD_DIR/bin/rtsp_puller" \
        -c "test/configs/pull_test_config.ini" \
        -t 60 \
        -d
    
    echo -e "${GREEN}拉流测试完成${NC}"
}

# 停止推流工具
stop_pusher() {
    echo -e "${YELLOW}停止推流工具...${NC}"
    
    if [ -f "$TEST_OUTPUT_DIR/push_tool.pid" ]; then
        PID=$(cat "$TEST_OUTPUT_DIR/push_tool.pid")
        if ps -p $PID > /dev/null; then
            kill $PID
            sleep 2
            # 强制杀死如果还在运行
            if ps -p $PID > /dev/null; then
                kill -9 $PID
            fi
        fi
        rm -f "$TEST_OUTPUT_DIR/push_tool.pid"
    fi
    
    # 确保所有相关进程都被停止
    pkill -f "rtsp_tool" || true
    pkill -f "rtsp_puller" || true
    
    echo -e "${GREEN}推流工具已停止${NC}"
}

# 分析测试结果
analyze_results() {
    echo -e "${YELLOW}分析测试结果...${NC}"
    
    CSV_FILE="$TEST_OUTPUT_DIR/pull_test_results.csv"
    LOG_FILE="$TEST_OUTPUT_DIR/pull_test.log"
    
    if [ -f "$CSV_FILE" ]; then
        echo -e "${GREEN}CSV结果文件: $CSV_FILE${NC}"
        echo "总记录数: $(wc -l < "$CSV_FILE")"
        
        # 统计SEI解析成功率
        if command -v awk >/dev/null 2>&1; then
            SUCCESS_COUNT=$(awk -F',' 'NR>1 && $8=="true" {count++} END {print count+0}' "$CSV_FILE")
            TOTAL_COUNT=$(awk 'NR>1 {count++} END {print count+0}' "$CSV_FILE")
            if [ $TOTAL_COUNT -gt 0 ]; then
                SUCCESS_RATE=$(awk "BEGIN {printf \"%.2f\", $SUCCESS_COUNT*100/$TOTAL_COUNT}")
                echo "SEI解析成功率: $SUCCESS_RATE% ($SUCCESS_COUNT/$TOTAL_COUNT)"
            fi
        fi
    fi
    
    if [ -f "$LOG_FILE" ]; then
        echo -e "${GREEN}日志文件: $LOG_FILE${NC}"
        echo "日志行数: $(wc -l < "$LOG_FILE")"
    fi
    
    echo -e "${BLUE}测试结果文件位于: $TEST_OUTPUT_DIR${NC}"
}

# 清理函数
cleanup() {
    echo -e "${YELLOW}清理测试环境...${NC}"
    stop_pusher
}

# 设置退出时清理
trap cleanup EXIT

# 主测试流程
main() {
    echo -e "${BLUE}开始RTSP拉流工具测试${NC}"
    
    # 检查参数
    if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
        echo "用法: $0 [选项]"
        echo "选项:"
        echo "  --skip-build    跳过编译步骤"
        echo "  --pull-only     仅运行拉流测试（假设推流工具已运行）"
        echo "  --help, -h      显示此帮助信息"
        exit 0
    fi
    
    # 检查依赖
    check_ffmpeg
    
    # 编译步骤
    if [ "$1" != "--skip-build" ]; then
        build_puller
        build_pusher
    fi
    
    # 如果不是仅拉流模式，启动推流工具
    if [ "$1" != "--pull-only" ]; then
        start_pusher
    fi
    
    # 运行拉流测试
    run_pull_test
    
    # 分析结果
    analyze_results
    
    echo -e "${GREEN}测试完成！${NC}"
}

# 运行主函数
main "$@"
