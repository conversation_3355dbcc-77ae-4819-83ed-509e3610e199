#ifndef RTSP_PULLER_H
#define RTSP_PULLER_H

#include <chrono>
#include <iostream>
#include <thread>
#include <vector>
#include <fstream>
#include <string>
#include <cstdint>
#include <stdexcept>
#include <sstream>
#include <algorithm>
#include <mutex>
#include <atomic>
#include <queue>
#include <condition_variable>
#include <iomanip>
#include <cstring>

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/time.h>
}

// 厂商类型枚举
enum class VendorType {
    AUTO = 0,
    HIKVISION = 1,
    DAHUA = 2,
    GENERIC = 3
};

// 厂商常量
struct VendorConstants {
    static const char* HIKVISION_UUID;
    static const char* DAHUA_UUID;
    static const size_t HIKVISION_PAYLOAD_SIZE = 0x21;  // 33字节
    static const size_t DAHUA_PAYLOAD_SIZE = 0x30;      // 48字节
    static const size_t TIMESTAMP_SIZE = 14;
    static const size_t UUID_SIZE = 16;
};

// 时间戳记录结构
struct TimingRecord {
    std::string stream_url;
    int64_t receive_time_ms;     // 接收时间
    int64_t sei_timestamp_ms;    // SEI中的Unix时间戳
    int64_t frame_index;         // 帧序号
    VendorType vendor_type;      // 检测到的厂商类型
    bool sei_parse_success;      // SEI解析是否成功
};

// 流统计信息
struct StreamStats {
    std::string url;
    std::atomic<int64_t> total_frames{0};
    std::atomic<int64_t> sei_success_count{0};
    std::atomic<int64_t> sei_fail_count{0};
    VendorType vendor_type = VendorType::AUTO;
    std::atomic<bool> is_connected{false};
    std::atomic<int64_t> last_receive_time{0};
    std::atomic<int64_t> last_sei_timestamp{0};

    // 复制构造函数
    StreamStats(const StreamStats& other)
        : url(other.url),
          total_frames(other.total_frames.load()),
          sei_success_count(other.sei_success_count.load()),
          sei_fail_count(other.sei_fail_count.load()),
          vendor_type(other.vendor_type),
          is_connected(other.is_connected.load()),
          last_receive_time(other.last_receive_time.load()),
          last_sei_timestamp(other.last_sei_timestamp.load()) {}

    // 赋值操作符
    StreamStats& operator=(const StreamStats& other) {
        if (this != &other) {
            url = other.url;
            total_frames.store(other.total_frames.load());
            sei_success_count.store(other.sei_success_count.load());
            sei_fail_count.store(other.sei_fail_count.load());
            vendor_type = other.vendor_type;
            is_connected.store(other.is_connected.load());
            last_receive_time.store(other.last_receive_time.load());
            last_sei_timestamp.store(other.last_sei_timestamp.load());
        }
        return *this;
    }

    // 默认构造函数
    StreamStats() = default;
};

// 配置结构
struct PullerConfig {
    std::vector<std::string> stream_urls;
    VendorType vendor_type = VendorType::AUTO;
    bool debug_mode = false;
    bool fallback_parsing = true;
    std::string csv_output = "pull_test.csv";
    std::string log_file = "pull_test.log";
    int console_update_interval = 1000;  // ms
    int statistics_interval = 10;        // seconds
    int connection_timeout = 5000;       // ms
    int read_timeout = 10000;           // ms
    int max_retries = 3;
    int retry_delay = 1000;             // ms
    int delay_warning_threshold = 100;   // ms
    bool timestamp_continuity_check = true;
    bool frame_drop_detection = true;
    int test_duration = 0;              // 0 = unlimited
    std::string transport = "tcp";     // rtsp 传输方式: tcp 或 udp
};

// SEI解析器类（基于enhanced_timestamp_extractor.cpp）
class VendorSEIParser {
private:
    bool debug_mode_;
    
    void dump_sei_hex(const uint8_t* data, size_t size, const std::string& title);
    bool validate_timestamp(int64_t timestamp);
    
public:
    VendorSEIParser(bool debug = false) : debug_mode_(debug) {}
    
    VendorType detect_vendor_from_uuid(uint8_t* sei_data, size_t sei_size);
    int64_t parse_hikvision_sei(uint8_t* data, size_t size);
    int64_t parse_dahua_sei(uint8_t* data, size_t size);
    int64_t parse_generic_sei(uint8_t* data, size_t size);
    int64_t parse_sei_timestamp(uint8_t* data, size_t size, VendorType vendor_hint = VendorType::AUTO);
    int64_t parse_packet_sei(AVPacket* pkt, VendorType hint = VendorType::AUTO);
};

// 时间戳分析器
class TimingAnalyzer {
private:
    std::vector<TimingRecord> records_;
    std::mutex records_mutex_;
    std::ofstream csv_file_;
    std::ofstream log_file_;
    
public:
    TimingAnalyzer(const std::string& csv_path, const std::string& log_path);
    ~TimingAnalyzer();
    
    void record_timing(const TimingRecord& record);
    void print_realtime_stats(const std::vector<StreamStats>& stats);
    void generate_final_report(const std::vector<StreamStats>& stats);
    void export_csv_header();
};

// 单路流接收器
class StreamReceiver {
private:
    std::string stream_url_;
    int stream_id_;
    AVFormatContext* input_ctx_ = nullptr;
    AVPacket pkt_;
    VendorSEIParser* sei_parser_;
    TimingAnalyzer* timing_analyzer_;
    StreamStats* stats_;
    PullerConfig* config_;
    std::atomic<bool> running_{false};
    std::thread receiver_thread_;
    
    int64_t get_current_timestamp_ms();
    int connect_stream();
    void disconnect_stream();
    int64_t extract_sei_timestamp(AVPacket* pkt);
    
public:
    StreamReceiver(const std::string& url, int id, VendorSEIParser* parser, 
                   TimingAnalyzer* analyzer, StreamStats* stats, PullerConfig* config);
    ~StreamReceiver();
    
    void start();
    void stop();
    void receive_loop();
    bool is_running() const { return running_; }
};

// 主拉流管理器
class RtspPuller {
private:
    PullerConfig config_;
    std::vector<std::unique_ptr<StreamReceiver>> receivers_;
    std::vector<StreamStats> stream_stats_;
    VendorSEIParser sei_parser_;
    TimingAnalyzer timing_analyzer_;
    std::atomic<bool> running_{false};
    std::thread stats_thread_;
    std::thread console_thread_;
    
    void stats_loop();
    void console_loop();
    void handle_user_input();
    
public:
    RtspPuller(const PullerConfig& config);
    ~RtspPuller();
    
    void start_pulling();
    void stop_pulling();
    void print_statistics();
    void wait_for_completion();
};

// 配置文件解析器
class ConfigParser {
public:
    static PullerConfig parse_config_file(const std::string& config_path);
    static PullerConfig parse_ini_format(const std::string& config_path);
};

// 工具函数
std::string trim(const std::string& str);
std::string vendor_type_to_string(VendorType type);
VendorType string_to_vendor_type(const std::string& str);
void print_help(const char* program_name);

#endif // RTSP_PULLER_H
