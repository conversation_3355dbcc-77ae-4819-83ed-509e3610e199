#include "rtsp_puller.h"
#include <csignal>
#include <cstdlib>

// 全局变量用于信号处理
static RtspPuller* g_puller = nullptr;

// 信号处理函数
void signal_handler(int signal) {
    std::cout << "\n收到信号 " << signal << "，正在优雅退出..." << std::endl;
    if (g_puller) {
        g_puller->stop_pulling();
    }
    exit(0);
}

// 解析命令行参数
PullerConfig parse_arguments(int argc, char* argv[]) {
    PullerConfig config;
    std::string config_file;
    bool stats_only = false;
    
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            print_help(argv[0]);
            exit(0);
        }
        else if (arg == "-c" || arg == "--config") {
            if (i + 1 >= argc) {
                std::cerr << "错误：" << arg << " 后缺少配置文件路径" << std::endl;
                exit(1);
            }
            config_file = argv[++i];
        }
        else if (arg == "-d" || arg == "--debug") {
            config.debug_mode = true;
        }
        else if (arg == "-v" || arg == "--vendor") {
            if (i + 1 >= argc) {
                std::cerr << "错误：" << arg << " 后缺少厂商类型" << std::endl;
                exit(1);
            }
            config.vendor_type = string_to_vendor_type(argv[++i]);
        }
        else if (arg == "-o" || arg == "--output") {
            if (i + 1 >= argc) {
                std::cerr << "错误：" << arg << " 后缺少输出文件路径" << std::endl;
                exit(1);
            }
            config.csv_output = argv[++i];
        }
        else if (arg == "-t" || arg == "--duration") {
            if (i + 1 >= argc) {
                std::cerr << "错误：" << arg << " 后缺少测试时长" << std::endl;
                exit(1);
            }
            config.test_duration = std::stoi(argv[++i]);
        }
        else if (arg == "--stats-only") {
            stats_only = true;
        }
        else if (arg == "--transport") {
            if (i + 1 >= argc) {
                std::cerr << "错误：--transport 后缺少 tcp|udp" << std::endl;
                exit(1);
            }
            std::string v = argv[++i];
            std::transform(v.begin(), v.end(), v.begin(), ::tolower);
            if (v == "tcp" || v == "udp") {
                config.transport = v;
            } else {
                std::cerr << "错误：--transport 必须是 tcp 或 udp" << std::endl;
                exit(1);
            }
        }
        else if (arg == "--low-latency") {
            config.low_latency = true;
        }
        else {
            std::cerr << "错误：未知选项 '" << arg << "'" << std::endl;
            std::cerr << "使用 --help 查看使用说明" << std::endl;
            exit(1);
        }
    }
    
    // 检查必需参数
    if (config_file.empty()) {
        std::cerr << "错误：必须指定 -c <配置文件>" << std::endl;
        print_help(argv[0]);
        exit(1);
    }
    
    // 从配置文件加载其他设置
    try {
        PullerConfig file_config = ConfigParser::parse_config_file(config_file);
        
        // 合并命令行参数和文件配置（命令行优先）
        if (config.stream_urls.empty()) {
            config.stream_urls = file_config.stream_urls;
        }
        
        // 只有当命令行未指定时才使用文件中的配置
        if (config.vendor_type == VendorType::AUTO && file_config.vendor_type != VendorType::AUTO) {
            config.vendor_type = file_config.vendor_type;
        }
        
        // 其他配置项直接使用文件配置
        config.fallback_parsing = file_config.fallback_parsing;
        config.console_update_interval = file_config.console_update_interval;
        config.statistics_interval = file_config.statistics_interval;
        config.connection_timeout = file_config.connection_timeout;
        config.read_timeout = file_config.read_timeout;
        config.max_retries = file_config.max_retries;
        config.retry_delay = file_config.retry_delay;
        config.delay_warning_threshold = file_config.delay_warning_threshold;
        config.timestamp_continuity_check = file_config.timestamp_continuity_check;
        config.frame_drop_detection = file_config.frame_drop_detection;
        
        // 如果命令行未指定输出文件，使用配置文件中的
        if (config.csv_output == "pull_test.csv") {
            config.csv_output = file_config.csv_output;
        }
        if (config.log_file == "pull_test.log") {
            config.log_file = file_config.log_file;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "配置文件解析错误: " << e.what() << std::endl;
        exit(1);
    }
    
    return config;
}

int main(int argc, char* argv[]) {
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 初始化FFmpeg
    avformat_network_init();
    
    try {
        // 解析参数和配置
        PullerConfig config = parse_arguments(argc, argv);
        
        // 创建拉流器
        RtspPuller puller(config);
        g_puller = &puller;
        
        // 启动拉流
        puller.start_pulling();
        
        // 等待完成或用户中断
        puller.wait_for_completion();
        
    } catch (const std::exception& e) {
        std::cerr << "程序错误: " << e.what() << std::endl;
        return -1;
    }
    
    // 清理FFmpeg网络
    avformat_network_deinit();
    
    std::cout << "程序正常退出" << std::endl;
    return 0;
}


