#include "rtsp_puller.h"

// 工具函数实现
std::string trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";
    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

std::string vendor_type_to_string(VendorType type) {
    switch (type) {
        case VendorType::AUTO: return "自动";
        case VendorType::HIKVISION: return "海康";
        case VendorType::DAHUA: return "大华";
        case VendorType::GENERIC: return "通用";
        default: return "未知";
    }
}

VendorType string_to_vendor_type(const std::string& str) {
    std::string lower_str = str;
    std::transform(lower_str.begin(), lower_str.end(), lower_str.begin(), ::tolower);
    
    if (lower_str == "auto") return VendorType::AUTO;
    if (lower_str == "hikvision" || lower_str == "hik") return VendorType::HIKVISION;
    if (lower_str == "dahua") return VendorType::DAHUA;
    if (lower_str == "generic") return VendorType::GENERIC;
    
    return VendorType::AUTO;
}

void print_help(const char* program_name) {
    std::cout << "=== RTSP拉流测试工具 ===" << std::endl;
    std::cout << "用于测试RTSP推流工具的时间戳准确性和延迟性能" << std::endl;
    std::cout << std::endl;
    std::cout << "用法: " << program_name << " -c <配置文件> [选项]" << std::endl;
    std::cout << std::endl;
    std::cout << "必需参数:" << std::endl;
    std::cout << "  -c, --config <文件>    配置文件路径" << std::endl;
    std::cout << std::endl;
    std::cout << "可选参数:" << std::endl;
    std::cout << "  -d, --debug            启用调试模式（显示SEI解析详情）" << std::endl;
    std::cout << "  -v, --vendor <类型>    强制指定厂商类型 (auto|hikvision|dahua|generic)" << std::endl;
    std::cout << "  -o, --output <文件>    CSV输出文件路径" << std::endl;
    std::cout << "  -t, --duration <秒>    测试持续时间（0=无限）" << std::endl;
    std::cout << "  --transport <tcp|udp>  选择 RTSP 传输方式（默认 tcp）" << std::endl;
    std::cout << "  --low-latency          启用低时延模式（减少缓冲）" << std::endl;
    std::cout << "  --stats-only           仅显示统计信息，不显示实时数据" << std::endl;
    std::cout << "  -h, --help             显示此帮助信息" << std::endl;
    std::cout << std::endl;
    std::cout << "配置文件格式 (INI):" << std::endl;
    std::cout << "[streams]" << std::endl;
    std::cout << "stream1=rtsp://ip:port/path1" << std::endl;
    std::cout << "stream2=rtsp://ip:port/path2" << std::endl;
    std::cout << "..." << std::endl;
    std::cout << std::endl;
    std::cout << "运行时命令:" << std::endl;
    std::cout << "  s - 显示当前统计信息" << std::endl;
    std::cout << "  r - 重置统计计数器" << std::endl;
    std::cout << "  d - 切换调试模式" << std::endl;
    std::cout << "  q - 退出程序" << std::endl;
    std::cout << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  " << program_name << " -c test_config.ini" << std::endl;
    std::cout << "  " << program_name << " -c test_config.ini -d -t 300" << std::endl;
    std::cout << "  " << program_name << " -c test_config.ini -v hikvision -o results.csv" << std::endl;
}

// RtspPuller实现
RtspPuller::RtspPuller(const PullerConfig& config) 
    : config_(config), sei_parser_(config.debug_mode), 
      timing_analyzer_(config.csv_output, config.log_file) {
    
    // 初始化流统计信息
    stream_stats_.resize(config_.stream_urls.size());
    for (size_t i = 0; i < config_.stream_urls.size(); i++) {
        stream_stats_[i].url = config_.stream_urls[i];
        stream_stats_[i].vendor_type = config_.vendor_type;
    }
    
    // 创建流接收器
    for (size_t i = 0; i < config_.stream_urls.size(); i++) {
        receivers_.emplace_back(std::make_unique<StreamReceiver>(
            config_.stream_urls[i], 
            i + 1,
            &sei_parser_,
            &timing_analyzer_,
            &stream_stats_[i],
            &config_
        ));
    }
}

RtspPuller::~RtspPuller() {
    stop_pulling();
}

void RtspPuller::start_pulling() {
    std::cout << "=== RTSP拉流测试工具启动 ===" << std::endl;
    std::cout << "流数量: " << config_.stream_urls.size() << std::endl;
    std::cout << "厂商类型: " << vendor_type_to_string(config_.vendor_type) << std::endl;
    std::cout << "调试模式: " << (config_.debug_mode ? "启用" : "禁用") << std::endl;
    if (config_.test_duration > 0) {
        std::cout << "测试时长: " << config_.test_duration << "秒" << std::endl;
    }
    std::cout << "CSV输出: " << config_.csv_output << std::endl;
    std::cout << std::endl;
    
    running_ = true;
    
    // 启动所有流接收器
    for (auto& receiver : receivers_) {
        receiver->start();
    }
    
    // 启动统计线程
    stats_thread_ = std::thread(&RtspPuller::stats_loop, this);
    
    // 启动控制台交互线程
    console_thread_ = std::thread(&RtspPuller::console_loop, this);
    
    std::cout << "所有流已启动，按 's' 查看统计信息，按 'q' 退出" << std::endl;
}

void RtspPuller::stop_pulling() {
    running_ = false;
    
    // 停止所有接收器
    for (auto& receiver : receivers_) {
        receiver->stop();
    }
    
    // 等待线程结束
    if (stats_thread_.joinable()) {
        stats_thread_.join();
    }
    if (console_thread_.joinable()) {
        console_thread_.join();
    }
    
    // 生成最终报告
    timing_analyzer_.generate_final_report(stream_stats_);
}

void RtspPuller::stats_loop() {
    auto start_time = std::chrono::steady_clock::now();
    
    while (running_) {
        std::this_thread::sleep_for(std::chrono::seconds(config_.statistics_interval));
        
        if (!running_) break;
        
        // 检查测试时长
        if (config_.test_duration > 0) {
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::steady_clock::now() - start_time).count();
            
            if (elapsed >= config_.test_duration) {
                std::cout << "\n测试时间到达，正在停止..." << std::endl;
                running_ = false;
                break;
            }
        }
        
        // 打印统计信息
        timing_analyzer_.print_realtime_stats(stream_stats_);
    }
}

void RtspPuller::console_loop() {
    while (running_) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        handle_user_input();
    }
}

void RtspPuller::handle_user_input() {
    // 简单的非阻塞输入检查
    // 注意：这是一个简化实现，在实际使用中可能需要更复杂的输入处理
    
    // 检查是否有输入可用（简化版本）
    // 在实际实现中，可以使用平台特定的非阻塞输入方法
}

void RtspPuller::print_statistics() {
    timing_analyzer_.print_realtime_stats(stream_stats_);
}

void RtspPuller::wait_for_completion() {
    // 等待所有接收器完成
    for (auto& receiver : receivers_) {
        while (receiver->is_running() && running_) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }
}


