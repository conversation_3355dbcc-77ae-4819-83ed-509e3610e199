# RTSP拉流工具 Makefile

# 编译器设置
CXX = g++
CXXFLAGS = -std=c++14 -Wall -Wextra -O2 -pthread

# FFmpeg库设置
FFMPEG_LIBS = -lavformat -lavcodec -lavutil

# 包含目录
INCLUDES = -I.

# 源文件
SOURCES = rtsp_puller_main.cpp \
          rtsp_puller.cpp \
          stream_receiver.cpp \
          timing_analyzer.cpp \
          vendor_sei_parser.cpp \
          config_parser.cpp

# 目标文件
OBJECTS = $(SOURCES:.cpp=.o)

# 可执行文件名
TARGET = rtsp_puller

# 默认目标
all: $(TARGET)

# 链接可执行文件
$(TARGET): $(OBJECTS)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(OBJECTS) $(FFMPEG_LIBS)
	@echo "编译完成: $(TARGET)"

# 编译目标文件
%.o: %.cpp rtsp_puller.h
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 清理
clean:
	rm -f $(OBJECTS) $(TARGET)
	@echo "清理完成"

# 安装到上级build目录
install: $(TARGET)
	@mkdir -p ../../build/bin
	cp $(TARGET) ../../build/bin/
	@echo "安装到 ../../build/bin/$(TARGET)"

# 调试版本
debug: CXXFLAGS += -g -DDEBUG
debug: $(TARGET)

# 检查FFmpeg依赖
check-deps:
	@echo "检查FFmpeg依赖..."
	@pkg-config --exists libavformat libavcodec libavutil || (echo "错误：未找到FFmpeg开发库" && exit 1)
	@echo "FFmpeg依赖检查通过"

# 显示帮助
help:
	@echo "可用目标:"
	@echo "  all       - 编译拉流工具 (默认)"
	@echo "  debug     - 编译调试版本"
	@echo "  clean     - 清理编译文件"
	@echo "  install   - 安装到build目录"
	@echo "  check-deps- 检查FFmpeg依赖"
	@echo "  help      - 显示此帮助信息"

.PHONY: all clean install debug check-deps help
