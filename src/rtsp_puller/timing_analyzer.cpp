#include "rtsp_puller.h"

TimingAnalyzer::TimingAnalyzer(const std::string& csv_path, const std::string& log_path) {
    // 打开CSV文件
    csv_file_.open(csv_path, std::ios::out | std::ios::trunc);
    if (csv_file_.is_open()) {
        export_csv_header();
    }
    
    // 打开日志文件
    log_file_.open(log_path, std::ios::out | std::ios::trunc);
    if (log_file_.is_open()) {
        log_file_ << "=== RTSP拉流测试日志 ===" << std::endl;
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        log_file_ << "开始时间: " << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << std::endl;
        log_file_ << std::endl;
    }
}

TimingAnalyzer::~TimingAnalyzer() {
    if (csv_file_.is_open()) {
        csv_file_.close();
    }
    if (log_file_.is_open()) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        log_file_ << std::endl << "结束时间: " << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S") << std::endl;
        log_file_.close();
    }
}

void TimingAnalyzer::export_csv_header() {
    csv_file_ << "timestamp,stream_url,receive_time_ms,sei_timestamp_ms,frame_index,vendor_type,sei_parse_success" << std::endl;
}

void TimingAnalyzer::record_timing(const TimingRecord& record) {
    std::lock_guard<std::mutex> lock(records_mutex_);
    
    // 添加到记录列表
    records_.push_back(record);
    
    // 写入CSV文件
    if (csv_file_.is_open()) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        csv_file_ << std::put_time(std::localtime(&time_t), "%Y-%m-%dT%H:%M:%S") << ","
                  << record.stream_url << ","
                  << record.receive_time_ms << ","
                  << record.sei_timestamp_ms << ","
                  << record.frame_index << ","
                  << vendor_type_to_string(record.vendor_type) << ","
                  << (record.sei_parse_success ? "true" : "false") << std::endl;
        csv_file_.flush();
    }
    
    // 写入详细日志
    if (log_file_.is_open()) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
        
        log_file_ << "[" << std::put_time(std::localtime(&time_t), "%H:%M:%S")
                  << "." << std::setfill('0') << std::setw(3) << ms.count() << "] "
                  << record.stream_url << " - "
                  << "接收时间=" << record.receive_time_ms << ", "
                  << "SEI时间戳=" << record.sei_timestamp_ms << ", "
                  << "厂商=" << vendor_type_to_string(record.vendor_type) << ", "
                  << "帧#" << record.frame_index
                  << (record.sei_parse_success ? "" : " [SEI解析失败]")
                  << std::endl;
        log_file_.flush();
    }
}

void TimingAnalyzer::print_realtime_stats(const std::vector<StreamStats>& stats) {
    std::cout << "\n=== 实时统计信息 ===" << std::endl;
    
    for (size_t i = 0; i < stats.size(); i++) {
        const auto& stat = stats[i];
        
        std::cout << "Stream" << (i+1) << " (" << vendor_type_to_string(stat.vendor_type) << "): ";
        
        if (!stat.is_connected) {
            std::cout << "未连接" << std::endl;
            continue;
        }
        
        int64_t total_frames = stat.total_frames.load();
        int64_t sei_success = stat.sei_success_count.load();
        int64_t sei_fail = stat.sei_fail_count.load();
        
        if (total_frames > 0) {
            double success_rate = (double)sei_success / total_frames * 100.0;

            std::cout << "帧数=" << total_frames
                      << ", SEI成功=" << sei_success << "(" << std::fixed << std::setprecision(1) << success_rate << "%)"
                      << ", 最新接收=" << stat.last_receive_time.load()
                      << ", 最新SEI=" << stat.last_sei_timestamp.load();

            if (sei_fail > 0) {
                std::cout << ", SEI失败=" << sei_fail;
            }
        } else {
            std::cout << "等待数据...";
        }
        std::cout << std::endl;
    }
    
    // 显示最后更新时间
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    std::cout << "更新时间: " << std::put_time(std::localtime(&time_t), "%H:%M:%S") << std::endl;
}

void TimingAnalyzer::generate_final_report(const std::vector<StreamStats>& stats) {
    std::cout << "\n=== 最终测试报告 ===" << std::endl;
    
    int64_t total_frames_all = 0;
    int64_t total_sei_success_all = 0;
    
    for (size_t i = 0; i < stats.size(); i++) {
        const auto& stat = stats[i];
        
        std::cout << "\nStream" << (i+1) << " (" << stat.url << "):" << std::endl;
        std::cout << "  厂商类型: " << vendor_type_to_string(stat.vendor_type) << std::endl;
        
        int64_t total_frames = stat.total_frames.load();
        int64_t sei_success = stat.sei_success_count.load();
        int64_t sei_fail = stat.sei_fail_count.load();
        
        if (total_frames > 0) {
            double success_rate = (double)sei_success / total_frames * 100.0;

            std::cout << "  总帧数: " << total_frames << std::endl;
            std::cout << "  SEI解析成功: " << sei_success << " (" << std::fixed << std::setprecision(2) << success_rate << "%)" << std::endl;
            std::cout << "  SEI解析失败: " << sei_fail << std::endl;
            std::cout << "  最新接收时间: " << stat.last_receive_time.load() << "ms" << std::endl;
            std::cout << "  最新SEI时间戳: " << stat.last_sei_timestamp.load() << "ms" << std::endl;

            // 累计全局统计
            total_frames_all += total_frames;
            total_sei_success_all += sei_success;
        } else {
            std::cout << "  无数据接收" << std::endl;
        }
    }
    
    // 全局统计
    if (total_frames_all > 0) {
        std::cout << "\n=== 全局统计 ===" << std::endl;
        double global_success_rate = (double)total_sei_success_all / total_frames_all * 100.0;

        std::cout << "总帧数: " << total_frames_all << std::endl;
        std::cout << "SEI解析成功率: " << std::fixed << std::setprecision(2) << global_success_rate << "%" << std::endl;
    }
    
    std::lock_guard<std::mutex> lock(records_mutex_);
    std::cout << "\n详细记录总数: " << records_.size() << std::endl;
}
