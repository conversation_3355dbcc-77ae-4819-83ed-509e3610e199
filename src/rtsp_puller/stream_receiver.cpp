#include "rtsp_puller.h"

StreamReceiver::StreamReceiver(const std::string& url, int id, VendorSEIParser* parser, 
                               TimingAnalyzer* analyzer, StreamStats* stats, PullerConfig* config)
    : stream_url_(url), stream_id_(id), sei_parser_(parser), timing_analyzer_(analyzer), 
      stats_(stats), config_(config) {
    av_init_packet(&pkt_);
    stats_->url = url;
}

StreamReceiver::~StreamReceiver() {
    stop();
    disconnect_stream();
    av_packet_unref(&pkt_);
}

int64_t StreamReceiver::get_current_timestamp_ms() {
    auto now = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch());
    return duration.count();
}

int StreamReceiver::connect_stream() {
    // 设置网络超时选项
    AVDictionary *options = nullptr;
    av_dict_set_int(&options, "rw_timeout", config_->connection_timeout * 1000, 0); // 微秒
    av_dict_set(&options, "rtsp_transport", "tcp", 0);  // 使用TCP传输
    
    // 打开RTSP流
    std::cout << "Stream" << stream_id_ << " 尝试连接: " << stream_url_ << std::endl;
    int ret = avformat_open_input(&input_ctx_, stream_url_.c_str(), nullptr, &options);
    av_dict_free(&options);

    if (ret < 0) {
        char errbuf[AV_ERROR_MAX_STRING_SIZE];
        av_strerror(ret, errbuf, sizeof(errbuf));
        std::cerr << "Stream" << stream_id_ << " 连接失败: " << errbuf << " (错误码: " << ret << ")" << std::endl;
        return -1;
    }

    std::cout << "Stream" << stream_id_ << " 连接成功!" << std::endl;
    
    // 获取流信息
    ret = avformat_find_stream_info(input_ctx_, nullptr);
    if (ret < 0) {
        std::cerr << "Stream" << stream_id_ << " 获取流信息失败" << std::endl;
        avformat_close_input(&input_ctx_);
        return -1;
    }
    
    // 查找视频流
    int video_stream_index = -1;
    for (unsigned i = 0; i < input_ctx_->nb_streams; i++) {
        if (input_ctx_->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            video_stream_index = i;
            break;
        }
    }
    
    if (video_stream_index == -1) {
        std::cerr << "Stream" << stream_id_ << " 未找到视频流" << std::endl;
        avformat_close_input(&input_ctx_);
        return -1;
    }
    
    stats_->is_connected = true;
    std::cout << "Stream" << stream_id_ << " 连接成功: " << stream_url_ << std::endl;
    return 0;
}

void StreamReceiver::disconnect_stream() {
    if (input_ctx_) {
        avformat_close_input(&input_ctx_);
        input_ctx_ = nullptr;
    }
    stats_->is_connected = false;
}

int64_t StreamReceiver::extract_sei_timestamp(AVPacket* pkt) {
    return sei_parser_->parse_packet_sei(pkt, config_->vendor_type);
}

void StreamReceiver::start() {
    running_ = true;
    receiver_thread_ = std::thread(&StreamReceiver::receive_loop, this);
}

void StreamReceiver::stop() {
    running_ = false;
    if (receiver_thread_.joinable()) {
        receiver_thread_.join();
    }
}

void StreamReceiver::receive_loop() {
    int retry_count = 0;
    
    while (running_) {
        // 尝试连接
        if (connect_stream() < 0) {
            retry_count++;
            if (retry_count > config_->max_retries) {
                std::cerr << "Stream" << stream_id_ << " 超过最大重试次数，退出" << std::endl;
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(config_->retry_delay));
            continue;
        }
        
        retry_count = 0; // 连接成功，重置重试计数
        
        // 主接收循环
        while (running_ && stats_->is_connected) {
            int ret = av_read_frame(input_ctx_, &pkt_);
            
            if (ret == AVERROR_EOF) {
                std::cout << "Stream" << stream_id_ << " 流结束" << std::endl;
                break;
            } else if (ret < 0) {
                char errbuf[AV_ERROR_MAX_STRING_SIZE];
                av_strerror(ret, errbuf, sizeof(errbuf));
                std::cerr << "Stream" << stream_id_ << " 读取错误: " << errbuf << std::endl;
                break;
            }
            
            // 记录接收时间
            int64_t receive_time = get_current_timestamp_ms();
            stats_->last_receive_time = receive_time;
            
            // 检查是否为视频帧
            if (input_ctx_->streams[pkt_.stream_index]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
                stats_->total_frames++;
                
                // 尝试解析SEI时间戳
                int64_t sei_timestamp = extract_sei_timestamp(&pkt_);
                
                TimingRecord record;
                record.stream_url = stream_url_;
                record.receive_time_ms = receive_time;
                record.sei_timestamp_ms = sei_timestamp;
                record.frame_index = stats_->total_frames.load();
                record.sei_parse_success = (sei_timestamp != -1);
                
                if (sei_timestamp != -1) {
                    // SEI解析成功
                    stats_->sei_success_count++;
                    stats_->last_sei_timestamp = sei_timestamp;

                    // 自动检测厂商类型（仅第一次）
                    if (stats_->vendor_type == VendorType::AUTO && record.sei_parse_success) {
                        // 通过SEI解析器检测厂商类型
                        VendorType detected = sei_parser_->detect_vendor_from_uuid(pkt_.data, pkt_.size);
                        if (detected != VendorType::GENERIC) {
                            stats_->vendor_type = detected;
                            record.vendor_type = detected;
                        }
                    } else {
                        record.vendor_type = stats_->vendor_type;
                    }
                } else {
                    // SEI解析失败
                    stats_->sei_fail_count++;
                    record.vendor_type = VendorType::GENERIC;
                }
                
                // 记录到分析器
                timing_analyzer_->record_timing(record);
                
                // 实时输出（非调试模式下减少输出频率）
                if (config_->debug_mode || stats_->total_frames % 30 == 0) {
                    auto now = std::chrono::system_clock::now();
                    auto time_t = std::chrono::system_clock::to_time_t(now);
                    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
                    
                    std::cout << "[" << std::put_time(std::localtime(&time_t), "%H:%M:%S")
                              << "." << std::setfill('0') << std::setw(3) << ms.count() << "] "
                              << "Stream" << stream_id_ << " (" << vendor_type_to_string(record.vendor_type) << "): "
                              << "接收时间=" << record.receive_time_ms << ", "
                              << "SEI时间戳=" << record.sei_timestamp_ms;
                    
                    if (!record.sei_parse_success) {
                        std::cout << " [SEI解析失败]";
                    }
                    std::cout << std::endl;
                }
            }
            
            av_packet_unref(&pkt_);
        }
        
        // 连接断开，准备重连
        disconnect_stream();
        if (running_) {
            std::cout << "Stream" << stream_id_ << " 连接断开，准备重连..." << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(config_->retry_delay));
        }
    }
    
    std::cout << "Stream" << stream_id_ << " 接收线程退出" << std::endl;
}
