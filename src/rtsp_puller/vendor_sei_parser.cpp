#include "rtsp_puller.h"

// 静态常量定义
const char* VendorConstants::HIKVISION_UUID = "HikTechnology"; // 支持多种变体
const char* VendorConstants::DAHUA_UUID = "DahuaTechnology";

// 十六进制转储函数
void VendorSEIParser::dump_sei_hex(const uint8_t* data, size_t size, const std::string& title) {
    if (!debug_mode_) return;
    
    std::cout << "[DEBUG] " << title << " (" << size << " bytes):" << std::endl;
    for (size_t i = 0; i < size && i < 64; i += 16) {
        std::cout << std::setfill('0') << std::setw(4) << std::hex << i << ": ";
        for (size_t j = 0; j < 16 && i + j < size && i + j < 64; j++) {
            std::cout << std::setfill('0') << std::setw(2) << std::hex 
                     << static_cast<int>(data[i + j]) << " ";
        }
        std::cout << std::endl;
    }
    if (size > 64) {
        std::cout << "... (truncated)" << std::endl;
    }
    std::cout << std::dec; // 恢复十进制输出
}

// 时间戳验证函数
bool VendorSEIParser::validate_timestamp(int64_t timestamp) {
    // 验证时间戳是否在合理范围内 (2020-2030年)
    return timestamp >= 1577836800000LL && timestamp <= 1893456000000LL;
}

// 检测厂商类型
VendorType VendorSEIParser::detect_vendor_from_uuid(uint8_t* sei_data, size_t sei_size) {
    if (sei_size < VendorConstants::UUID_SIZE) {
        return VendorType::GENERIC;
    }

    // 在整个SEI数据中搜索厂商UUID
    for (size_t i = 0; i <= sei_size - 15; i++) {
        // 检查是否包含海康UUID（支持多种变体）
        if (memcmp(sei_data + i, VendorConstants::HIKVISION_UUID, 13) == 0) {
            if (debug_mode_) {
                std::cout << "[DEBUG] 检测到海康威视SEI格式" << std::endl;
            }
            return VendorType::HIKVISION;
        }

        // 检查是否包含大华UUID
        if (memcmp(sei_data + i, VendorConstants::DAHUA_UUID, 15) == 0) {
            if (debug_mode_) {
                std::cout << "[DEBUG] 检测到大华SEI格式" << std::endl;
            }
            return VendorType::DAHUA;
        }
    }

    return VendorType::GENERIC;
}

// 解析海康威视SEI时间戳
int64_t VendorSEIParser::parse_hikvision_sei(uint8_t* data, size_t size) {
    if (debug_mode_) {
        std::cout << "[DEBUG] 使用 海康威视 SEI解析器" << std::endl;
    }
    
    // 标准格式：查找UUID后的时间戳
    for (size_t i = 0; i <= size - 33; i++) {
        if (memcmp(data + i, VendorConstants::HIKVISION_UUID, 13) == 0) {
            size_t timestamp_offset = i + 16; // UUID + 3字节偏移
            
            if (timestamp_offset + VendorConstants::TIMESTAMP_SIZE <= size) {
                char timestamp[VendorConstants::TIMESTAMP_SIZE + 1] = {0};
                memcpy(timestamp, data + timestamp_offset, VendorConstants::TIMESTAMP_SIZE);
                
                if (debug_mode_) {
                    std::cout << "[DEBUG] 海康SEI时间戳字符串(标准格式): " << timestamp << std::endl;
                    dump_sei_hex(data + i, std::min(size_t(36), size - i), "海康SEI负载(标准)");
                }
                
                try {
                    int64_t timestamp_ms = std::stoll(timestamp);
                    if (validate_timestamp(timestamp_ms)) {
                        return timestamp_ms;
                    }
                } catch (const std::exception &e) {
                    if (debug_mode_) {
                        std::cerr << "[DEBUG] 海康时间戳解析失败: " << e.what() << std::endl;
                    }
                }
            }
        }
    }
    
    // 变体格式：动态搜索UUID位置
    for (size_t i = 0; i <= size - 36; i++) {
        if (memcmp(data + i, VendorConstants::HIKVISION_UUID, 13) == 0) {
            // 尝试不同的偏移量
            size_t offsets[] = {19, 20, 18, 21, 17};
            for (size_t offset : offsets) {
                if (i + offset + VendorConstants::TIMESTAMP_SIZE <= size) {
                    char timestamp[VendorConstants::TIMESTAMP_SIZE + 1] = {0};
                    memcpy(timestamp, data + i + offset, VendorConstants::TIMESTAMP_SIZE);
                    
                    if (debug_mode_) {
                        std::cout << "[DEBUG] 海康SEI时间戳字符串(变体格式,偏移" << offset << "): " << timestamp << std::endl;
                    }
                    
                    try {
                        int64_t timestamp_ms = std::stoll(timestamp);
                        if (validate_timestamp(timestamp_ms)) {
                            return timestamp_ms;
                        }
                    } catch (const std::exception &e) {
                        continue;
                    }
                }
            }
        }
    }
    
    return -1;
}

// 解析大华SEI时间戳
int64_t VendorSEIParser::parse_dahua_sei(uint8_t* data, size_t size) {
    if (debug_mode_) {
        std::cout << "[DEBUG] 使用 大华 SEI解析器" << std::endl;
    }
    
    // 标准格式：查找UUID后的时间戳
    for (size_t i = 0; i <= size - 48; i++) {
        if (memcmp(data + i, VendorConstants::DAHUA_UUID, 15) == 0) {
            size_t timestamp_offset = i + 16; // UUID + 1字节偏移
            
            if (timestamp_offset + VendorConstants::TIMESTAMP_SIZE <= size) {
                char timestamp[VendorConstants::TIMESTAMP_SIZE + 1] = {0};
                memcpy(timestamp, data + timestamp_offset, VendorConstants::TIMESTAMP_SIZE);
                
                if (debug_mode_) {
                    std::cout << "[DEBUG] 大华SEI时间戳字符串(标准格式): " << timestamp << std::endl;
                    dump_sei_hex(data + i, std::min(size_t(48), size - i), "大华SEI负载(标准)");
                }
                
                try {
                    int64_t timestamp_ms = std::stoll(timestamp);
                    if (validate_timestamp(timestamp_ms)) {
                        return timestamp_ms;
                    }
                } catch (const std::exception &e) {
                    if (debug_mode_) {
                        std::cerr << "[DEBUG] 大华时间戳解析失败: " << e.what() << std::endl;
                    }
                }
            }
        }
    }
    
    // 变体格式：动态搜索UUID位置
    for (size_t i = 0; i <= size - 51; i++) {
        if (memcmp(data + i, VendorConstants::DAHUA_UUID, 15) == 0) {
            // 尝试不同的偏移量
            size_t offsets[] = {19, 20, 18, 21, 17};
            for (size_t offset : offsets) {
                if (i + offset + VendorConstants::TIMESTAMP_SIZE <= size) {
                    char timestamp[VendorConstants::TIMESTAMP_SIZE + 1] = {0};
                    memcpy(timestamp, data + i + offset, VendorConstants::TIMESTAMP_SIZE);
                    
                    if (debug_mode_) {
                        std::cout << "[DEBUG] 大华SEI时间戳字符串(变体格式,偏移" << offset << "): " << timestamp << std::endl;
                    }
                    
                    try {
                        int64_t timestamp_ms = std::stoll(timestamp);
                        if (validate_timestamp(timestamp_ms)) {
                            return timestamp_ms;
                        }
                    } catch (const std::exception &e) {
                        continue;
                    }
                }
            }
        }
    }
    
    return -1;
}

// 通用SEI解析（备选方案）
int64_t VendorSEIParser::parse_generic_sei(uint8_t* data, size_t size) {
    if (debug_mode_) {
        std::cout << "[DEBUG] 使用通用SEI解析器" << std::endl;
        dump_sei_hex(data, size, "通用SEI数据");
    }
    
    // 尝试多个可能的时间戳偏移位置
    const size_t possible_offsets[] = {19, 20, 18, 21, 17, 22, 16, 15};
    
    for (size_t offset : possible_offsets) {
        if (offset + VendorConstants::TIMESTAMP_SIZE <= size) {
            char timestamp[VendorConstants::TIMESTAMP_SIZE + 1] = {0};
            memcpy(timestamp, data + offset, VendorConstants::TIMESTAMP_SIZE);
            
            if (debug_mode_) {
                std::cout << "[DEBUG] 通用SEI时间戳字符串(偏移" << offset << "): " << timestamp << std::endl;
            }
            
            try {
                int64_t timestamp_ms = std::stoll(timestamp);
                if (validate_timestamp(timestamp_ms)) {
                    return timestamp_ms;
                }
            } catch (const std::exception &e) {
                continue;
            }
        }
    }
    
    return -1;
}

// 主要的SEI解析函数
int64_t VendorSEIParser::parse_sei_timestamp(uint8_t* data, size_t size, VendorType vendor_hint) {
    if (size < 20) return -1;
    
    VendorType detected_vendor = vendor_hint;
    
    // 如果是自动检测模式，先检测厂商
    if (vendor_hint == VendorType::AUTO) {
        detected_vendor = detect_vendor_from_uuid(data, size);
    }
    
    int64_t result = -1;
    
    // 根据厂商类型选择解析策略
    switch (detected_vendor) {
        case VendorType::HIKVISION:
            result = parse_hikvision_sei(data, size);
            break;
        case VendorType::DAHUA:
            result = parse_dahua_sei(data, size);
            break;
        case VendorType::GENERIC:
        default:
            result = parse_generic_sei(data, size);
            break;
    }
    
    // 如果厂商特定解析失败，尝试备选方案
    if (result == -1 && detected_vendor != VendorType::GENERIC) {
        if (debug_mode_) {
            std::cout << "[DEBUG] 厂商特定解析失败，尝试通用解析" << std::endl;
        }
        result = parse_generic_sei(data, size);
    }
    
    return result;
}

// 解析数据包中的SEI时间戳
int64_t VendorSEIParser::parse_packet_sei(AVPacket* pkt, VendorType hint) {
    if (!pkt || !pkt->data || pkt->size < 4) {
        return -1;
    }
    
    uint8_t* data = pkt->data;
    size_t size = pkt->size;
    size_t pos = 0;
    
    // 扫描数据包寻找SEI NAL单元
    while (pos < size) {
        // 查找H.264起始码
        if (pos + 3 < size && data[pos] == 0x00 && data[pos + 1] == 0x00 && data[pos + 2] == 0x01) {
            uint8_t nal_type = data[pos + 3] & 0x1F;
            
            if (nal_type == 6) { // SEI NALU
                size_t sei_payload_start = pos + 4; // 跳过起始码和NAL头
                size_t sei_payload_size = 0;
                
                // 找到SEI数据的结束位置
                while (sei_payload_start + sei_payload_size < size &&
                       data[sei_payload_start + sei_payload_size] != 0x80) {
                    sei_payload_size++;
                }
                
                if (sei_payload_start + sei_payload_size < size && sei_payload_size > 20) {
                    // 解析SEI时间戳
                    int64_t timestamp = parse_sei_timestamp(
                        data + sei_payload_start,
                        sei_payload_size,
                        hint
                    );
                    
                    if (timestamp != -1) {
                        return timestamp;
                    }
                }
                
                // 跳过整个SEI数据块
                pos = sei_payload_start + sei_payload_size + 1;
                continue;
            }
        }
        // 查找4字节起始码
        else if (pos + 4 < size && data[pos] == 0x00 && data[pos + 1] == 0x00 && 
                 data[pos + 2] == 0x00 && data[pos + 3] == 0x01) {
            uint8_t nal_type = data[pos + 4] & 0x1F;
            
            if (nal_type == 6) { // SEI NALU
                size_t sei_payload_start = pos + 5; // 跳过4字节起始码和NAL头
                size_t sei_payload_size = 0;
                
                // 找到SEI数据的结束位置
                while (sei_payload_start + sei_payload_size < size &&
                       data[sei_payload_start + sei_payload_size] != 0x80) {
                    sei_payload_size++;
                }
                
                if (sei_payload_start + sei_payload_size < size && sei_payload_size > 20) {
                    // 解析SEI时间戳
                    int64_t timestamp = parse_sei_timestamp(
                        data + sei_payload_start,
                        sei_payload_size,
                        hint
                    );
                    
                    if (timestamp != -1) {
                        return timestamp;
                    }
                }
                
                // 跳过整个SEI数据块
                pos = sei_payload_start + sei_payload_size + 1;
                continue;
            }
        }
        pos++;
    }
    
    return -1;
}
