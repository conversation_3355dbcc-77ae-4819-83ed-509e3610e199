# RTSP拉流测试配置文件示例
# 用于测试RTSP推流工具的时间戳准确性和延迟性能

[streams]
# 示例RTSP流地址（请替换为实际的RTSP地址）
stream1=rtsp://192.168.1.100:554/stream1
stream2=rtsp://192.168.1.101:554/stream2
# stream3=rtsp://192.168.1.102:554/stream3

[settings]
# 厂商类型：auto, hikvision, dahua, generic
vendor_type=auto

# 调试模式
debug_mode=false

# 输出文件
csv_output=pull_test.csv
log_file=pull_test.log

# 更新间隔（毫秒）
console_update_interval=1000

# 统计间隔（秒）
statistics_interval=10

# 连接超时（毫秒）
connection_timeout=5000

# 读取超时（毫秒）
read_timeout=10000

# 最大重试次数
max_retries=3

# 重试延迟（毫秒）
retry_delay=1000

# 延迟警告阈值（毫秒）
delay_warning_threshold=100

# 测试持续时间（秒，0=无限）
test_duration=0
