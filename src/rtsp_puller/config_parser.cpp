#include "rtsp_puller.h"

PullerConfig ConfigParser::parse_config_file(const std::string& config_path) {
    return parse_ini_format(config_path);
}

PullerConfig ConfigParser::parse_ini_format(const std::string& config_path) {
    PullerConfig config;
    
    std::ifstream file(config_path);
    if (!file.is_open()) {
        throw std::runtime_error("无法打开配置文件: " + config_path);
    }
    
    std::string line;
    std::string current_section;
    int line_num = 0;
    
    while (std::getline(file, line)) {
        line_num++;
        line = trim(line);
        
        // 跳过空行和注释行
        if (line.empty() || line[0] == '#') continue;
        
        // 检查是否是节标识行 [section_name]
        if (line[0] == '[' && line.back() == ']') {
            current_section = line.substr(1, line.length() - 2);
            current_section = trim(current_section);
            continue;
        }
        
        // 解析键值对
        size_t eq_pos = line.find('=');
        if (eq_pos == std::string::npos) {
            std::cerr << "配置错误（第 " << line_num << " 行）：缺少等号" << std::endl;
            continue;
        }
        
        std::string key = trim(line.substr(0, eq_pos));
        std::string value = trim(line.substr(eq_pos + 1));
        
        // 根据节处理不同配置
        if (current_section == "streams") {
            if (key.find("stream") == 0) {
                config.stream_urls.push_back(value);
            }
        }
        else if (current_section == "sei_parsing") {
            if (key == "vendor_type") {
                config.vendor_type = string_to_vendor_type(value);
            } else if (key == "debug_mode") {
                config.debug_mode = (value == "true" || value == "1");
            } else if (key == "fallback_parsing") {
                config.fallback_parsing = (value == "true" || value == "1");
            }
        }
        else if (current_section == "output") {
            if (key == "console_update_interval") {
                config.console_update_interval = std::stoi(value);
            } else if (key == "csv_output") {
                config.csv_output = value;
            } else if (key == "log_file") {
                config.log_file = value;
            } else if (key == "statistics_interval") {
                config.statistics_interval = std::stoi(value);
            }
        }
        else if (current_section == "network") {
            if (key == "connection_timeout") {
                config.connection_timeout = std::stoi(value);
            } else if (key == "read_timeout") {
                config.read_timeout = std::stoi(value);
            } else if (key == "max_retries") {
                config.max_retries = std::stoi(value);
            } else if (key == "retry_delay") {
                config.retry_delay = std::stoi(value);
            } else if (key == "transport") {
                std::string v = value;
                std::transform(v.begin(), v.end(), v.begin(), ::tolower);
                if (v == "tcp" || v == "udp") config.transport = v;
            } else if (key == "low_latency") {
                config.low_latency = (value == "true" || value == "1");
            }
        }
        else if (current_section == "analysis") {
            if (key == "delay_warning_threshold") {
                config.delay_warning_threshold = std::stoi(value);
            } else if (key == "timestamp_continuity_check") {
                config.timestamp_continuity_check = (value == "true" || value == "1");
            } else if (key == "frame_drop_detection") {
                config.frame_drop_detection = (value == "true" || value == "1");
            }
        }
    }
    
    file.close();
    
    // 验证配置
    if (config.stream_urls.empty()) {
        throw std::runtime_error("配置文件中未找到有效的流地址");
    }
    
    if (config.stream_urls.size() > 8) {
        std::cerr << "警告：流数量过多（" << config.stream_urls.size() << "），建议不超过8路" << std::endl;
    }
    
    return config;
}


