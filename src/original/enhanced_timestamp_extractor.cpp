#include <cstdint>
#include <cstring>
#include <fstream>
#include <iostream>
#include <stdexcept>
#include <string>
#include <vector>
#include <iomanip>

extern "C"
{
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/time.h>
}

// 厂商类型枚举
enum class VendorType {
    AUTO,
    HIKVISION,
    DAHUA,
    GENERIC
};

// 厂商常量
struct VendorConstants {
    static const char* HIKVISION_UUID;
    static const char* DAHUA_UUID;
    static const size_t TIMESTAMP_SIZE = 14;
    static const size_t UUID_SIZE = 16;
};

const char* VendorConstants::HIKVISION_UUID = "HikTechnology"; // 支持多种变体
const char* VendorConstants::DAHUA_UUID = "DahuaTechnology";

// 命令行选项结构
struct CliOptions {
    std::string input_file;
    std::string output_file;
    VendorType vendor = VendorType::AUTO;
    bool debug_mode = false;
    bool help = false;
};

// SEI解析器类
class VendorSEIParser {
private:
    bool debug_mode_;
    
    // 十六进制转储函数
    void dump_sei_hex(const uint8_t* data, size_t size, const std::string& title) {
        if (!debug_mode_) return;
        
        std::cout << "[DEBUG] " << title << " (" << size << " bytes):" << std::endl;
        for (size_t i = 0; i < size && i < 64; i += 16) {
            std::cout << std::setfill('0') << std::setw(4) << std::hex << i << ": ";
            for (size_t j = 0; j < 16 && i + j < size && i + j < 64; j++) {
                std::cout << std::setfill('0') << std::setw(2) << std::hex 
                         << static_cast<int>(data[i + j]) << " ";
            }
            std::cout << std::endl;
        }
        if (size > 64) {
            std::cout << "... (truncated)" << std::endl;
        }
        std::cout << std::dec; // 恢复十进制输出
    }
    
    // 时间戳验证函数
    bool validate_timestamp(int64_t timestamp) {
        // 验证时间戳是否在合理范围内 (2020-2030年)
        return timestamp >= 1577836800000LL && timestamp <= 1893456000000LL;
    }

public:
    VendorSEIParser(bool debug = false) : debug_mode_(debug) {}
    
    // 检测厂商类型
    VendorType detect_vendor_from_uuid(uint8_t* sei_data, size_t sei_size) {
        if (sei_size < VendorConstants::UUID_SIZE) {
            return VendorType::GENERIC;
        }

        // 在整个SEI数据中搜索厂商UUID
        for (size_t i = 0; i <= sei_size - 15; i++) {
            // 检查是否包含海康UUID（支持多种变体）
            if (memcmp(sei_data + i, VendorConstants::HIKVISION_UUID, 13) == 0) {
                if (debug_mode_) {
                    std::cout << "[DEBUG] 检测到海康威视SEI格式" << std::endl;
                }
                return VendorType::HIKVISION;
            }

            // 检查是否包含大华UUID
            if (memcmp(sei_data + i, VendorConstants::DAHUA_UUID, 15) == 0) {
                if (debug_mode_) {
                    std::cout << "[DEBUG] 检测到大华SEI格式" << std::endl;
                }
                return VendorType::DAHUA;
            }
        }

        return VendorType::GENERIC;
    }
    
    // 解析海康威视SEI时间戳
    int64_t parse_hikvision_sei(uint8_t* data, size_t size) {
        if (debug_mode_) {
            std::cout << "[DEBUG] 使用 海康威视 SEI解析器" << std::endl;
        }
        
        // 标准格式：查找UUID后的时间戳
        for (size_t i = 0; i <= size - 33; i++) {
            if (memcmp(data + i, VendorConstants::HIKVISION_UUID, 13) == 0) {
                size_t timestamp_offset = i + 16; // UUID + 3字节偏移
                
                if (timestamp_offset + VendorConstants::TIMESTAMP_SIZE <= size) {
                    char timestamp[VendorConstants::TIMESTAMP_SIZE + 1] = {0};
                    memcpy(timestamp, data + timestamp_offset, VendorConstants::TIMESTAMP_SIZE);
                    
                    if (debug_mode_) {
                        std::cout << "[DEBUG] 海康SEI时间戳字符串(标准格式): " << timestamp << std::endl;
                        dump_sei_hex(data + i, std::min(size_t(36), size - i), "海康SEI负载(标准)");
                    }
                    
                    try {
                        int64_t timestamp_ms = std::stoll(timestamp);
                        if (validate_timestamp(timestamp_ms)) {
                            return timestamp_ms;
                        }
                    } catch (const std::exception &e) {
                        if (debug_mode_) {
                            std::cerr << "[DEBUG] 海康时间戳解析失败: " << e.what() << std::endl;
                        }
                    }
                }
            }
        }
        
        // 变体格式：动态搜索UUID位置
        for (size_t i = 0; i <= size - 36; i++) {
            if (memcmp(data + i, VendorConstants::HIKVISION_UUID, 13) == 0) {
                // 尝试不同的偏移量
                size_t offsets[] = {19, 20, 18, 21, 17};
                for (size_t offset : offsets) {
                    if (i + offset + VendorConstants::TIMESTAMP_SIZE <= size) {
                        char timestamp[VendorConstants::TIMESTAMP_SIZE + 1] = {0};
                        memcpy(timestamp, data + i + offset, VendorConstants::TIMESTAMP_SIZE);
                        
                        if (debug_mode_) {
                            std::cout << "[DEBUG] 海康SEI时间戳字符串(变体格式,偏移" << offset << "): " << timestamp << std::endl;
                        }
                        
                        try {
                            int64_t timestamp_ms = std::stoll(timestamp);
                            if (validate_timestamp(timestamp_ms)) {
                                return timestamp_ms;
                            }
                        } catch (const std::exception &e) {
                            continue;
                        }
                    }
                }
            }
        }
        
        return -1;
    }
    
    // 解析大华SEI时间戳
    int64_t parse_dahua_sei(uint8_t* data, size_t size) {
        if (debug_mode_) {
            std::cout << "[DEBUG] 使用 大华 SEI解析器" << std::endl;
        }
        
        // 标准格式：查找UUID后的时间戳
        for (size_t i = 0; i <= size - 48; i++) {
            if (memcmp(data + i, VendorConstants::DAHUA_UUID, 15) == 0) {
                size_t timestamp_offset = i + 16; // UUID + 1字节偏移
                
                if (timestamp_offset + VendorConstants::TIMESTAMP_SIZE <= size) {
                    char timestamp[VendorConstants::TIMESTAMP_SIZE + 1] = {0};
                    memcpy(timestamp, data + timestamp_offset, VendorConstants::TIMESTAMP_SIZE);
                    
                    if (debug_mode_) {
                        std::cout << "[DEBUG] 大华SEI时间戳字符串(标准格式): " << timestamp << std::endl;
                        dump_sei_hex(data + i, std::min(size_t(48), size - i), "大华SEI负载(标准)");
                    }
                    
                    try {
                        int64_t timestamp_ms = std::stoll(timestamp);
                        if (validate_timestamp(timestamp_ms)) {
                            return timestamp_ms;
                        }
                    } catch (const std::exception &e) {
                        if (debug_mode_) {
                            std::cerr << "[DEBUG] 大华时间戳解析失败: " << e.what() << std::endl;
                        }
                    }
                }
            }
        }
        
        // 变体格式：动态搜索UUID位置
        for (size_t i = 0; i <= size - 51; i++) {
            if (memcmp(data + i, VendorConstants::DAHUA_UUID, 15) == 0) {
                // 尝试不同的偏移量
                size_t offsets[] = {19, 20, 18, 21, 17};
                for (size_t offset : offsets) {
                    if (i + offset + VendorConstants::TIMESTAMP_SIZE <= size) {
                        char timestamp[VendorConstants::TIMESTAMP_SIZE + 1] = {0};
                        memcpy(timestamp, data + i + offset, VendorConstants::TIMESTAMP_SIZE);
                        
                        if (debug_mode_) {
                            std::cout << "[DEBUG] 大华SEI时间戳字符串(变体格式,偏移" << offset << "): " << timestamp << std::endl;
                        }
                        
                        try {
                            int64_t timestamp_ms = std::stoll(timestamp);
                            if (validate_timestamp(timestamp_ms)) {
                                return timestamp_ms;
                            }
                        } catch (const std::exception &e) {
                            continue;
                        }
                    }
                }
            }
        }
        
        return -1;
    }
    
    // 通用SEI解析（备选方案）
    int64_t parse_generic_sei(uint8_t* data, size_t size) {
        if (debug_mode_) {
            std::cout << "[DEBUG] 使用通用SEI解析器" << std::endl;
            dump_sei_hex(data, size, "通用SEI数据");
        }
        
        // 尝试多个可能的时间戳偏移位置
        const size_t possible_offsets[] = {19, 20, 18, 21, 17, 22, 16, 15};
        
        for (size_t offset : possible_offsets) {
            if (offset + VendorConstants::TIMESTAMP_SIZE <= size) {
                char timestamp[VendorConstants::TIMESTAMP_SIZE + 1] = {0};
                memcpy(timestamp, data + offset, VendorConstants::TIMESTAMP_SIZE);
                
                if (debug_mode_) {
                    std::cout << "[DEBUG] 通用SEI时间戳字符串(偏移" << offset << "): " << timestamp << std::endl;
                }
                
                try {
                    int64_t timestamp_ms = std::stoll(timestamp);
                    if (validate_timestamp(timestamp_ms)) {
                        return timestamp_ms;
                    }
                } catch (const std::exception &e) {
                    continue;
                }
            }
        }
        
        return -1;
    }
    
    // 主要的SEI解析函数
    int64_t parse_sei_timestamp(uint8_t* data, size_t size, VendorType vendor_hint = VendorType::AUTO) {
        if (size < 20) return -1;
        
        VendorType detected_vendor = vendor_hint;
        
        // 如果是自动检测模式，先检测厂商
        if (vendor_hint == VendorType::AUTO) {
            detected_vendor = detect_vendor_from_uuid(data, size);
        }
        
        int64_t result = -1;
        
        // 根据厂商类型选择解析策略
        switch (detected_vendor) {
            case VendorType::HIKVISION:
                result = parse_hikvision_sei(data, size);
                break;
            case VendorType::DAHUA:
                result = parse_dahua_sei(data, size);
                break;
            case VendorType::GENERIC:
            default:
                result = parse_generic_sei(data, size);
                break;
        }
        
        // 如果厂商特定解析失败，尝试备选方案
        if (result == -1 && detected_vendor != VendorType::GENERIC) {
            if (debug_mode_) {
                std::cout << "[DEBUG] 厂商特定解析失败，尝试通用解析" << std::endl;
            }
            result = parse_generic_sei(data, size);
        }
        
        return result;
    }
};

// 解析命令行参数
CliOptions parse_arguments(int argc, char* argv[]) {
    CliOptions options;

    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--help" || arg == "-h") {
            options.help = true;
            return options;
        } else if (arg == "--debug") {
            options.debug_mode = true;
        } else if (arg == "--vendor") {
            if (i + 1 < argc) {
                std::string vendor = argv[++i];
                if (vendor == "auto") {
                    options.vendor = VendorType::AUTO;
                } else if (vendor == "hikvision") {
                    options.vendor = VendorType::HIKVISION;
                } else if (vendor == "dahua") {
                    options.vendor = VendorType::DAHUA;
                } else if (vendor == "generic") {
                    options.vendor = VendorType::GENERIC;
                } else {
                    std::cerr << "未知的厂商类型: " << vendor << std::endl;
                    std::cerr << "支持的厂商: auto, hikvision, dahua, generic" << std::endl;
                    exit(1);
                }
            } else {
                std::cerr << "--vendor 选项需要参数" << std::endl;
                exit(1);
            }
        } else if (options.input_file.empty()) {
            options.input_file = arg;
        } else if (options.output_file.empty()) {
            options.output_file = arg;
        }
    }

    return options;
}

// 显示帮助信息
void show_help(const char* program_name) {
    std::cout << "增强版SEI时间戳提取工具" << std::endl;
    std::cout << "支持海康威视、大华等多厂商SEI格式" << std::endl;
    std::cout << std::endl;
    std::cout << "用法: " << program_name << " [选项] <输入视频文件> [输出时间戳文件]" << std::endl;
    std::cout << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  --vendor <type>    指定厂商类型 (auto|hikvision|dahua|generic)" << std::endl;
    std::cout << "                     默认: auto (自动检测)" << std::endl;
    std::cout << "  --debug            启用调试模式，显示详细解析信息" << std::endl;
    std::cout << "  --help, -h         显示此帮助信息" << std::endl;
    std::cout << std::endl;
    std::cout << "支持的厂商格式:" << std::endl;
    std::cout << "  海康威视 (Hikvision): 标准33字节和36字节变体格式" << std::endl;
    std::cout << "  大华 (Dahua):        标准48字节和51字节变体格式" << std::endl;
    std::cout << "  通用格式:            多偏移量自适应解析" << std::endl;
    std::cout << std::endl;
    std::cout << "示例:" << std::endl;
    std::cout << "  " << program_name << " video.ts                    # 自动检测并提取到video.txt" << std::endl;
    std::cout << "  " << program_name << " --vendor hikvision video.ts # 强制使用海康格式" << std::endl;
    std::cout << "  " << program_name << " --debug video.ts output.txt # 调试模式" << std::endl;
}

// 提取SEI时间戳并保存到文件
bool extract_sei_timestamps(const CliOptions& options) {
    AVFormatContext *format_ctx = nullptr;
    if (avformat_open_input(&format_ctx, options.input_file.c_str(), nullptr, nullptr) < 0) {
        std::cerr << "无法打开输入文件: " << options.input_file << std::endl;
        return false;
    }

    if (avformat_find_stream_info(format_ctx, nullptr) < 0) {
        std::cerr << "无法获取流信息: " << options.input_file << std::endl;
        avformat_close_input(&format_ctx);
        return false;
    }

    int video_stream_index = -1;
    for (unsigned i = 0; i < format_ctx->nb_streams; ++i) {
        if (format_ctx->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            video_stream_index = i;
            break;
        }
    }

    if (video_stream_index == -1) {
        std::cerr << "未找到视频流" << std::endl;
        avformat_close_input(&format_ctx);
        return false;
    }

    // 打开输出文件
    std::ofstream out_file(options.output_file, std::ios::out | std::ios::trunc);
    if (!out_file.is_open()) {
        std::cerr << "无法打开输出文件: " << options.output_file << std::endl;
        avformat_close_input(&format_ctx);
        return false;
    }

    // 创建SEI解析器
    VendorSEIParser parser(options.debug_mode);

    std::string vendor_name;
    switch (options.vendor) {
        case VendorType::AUTO: vendor_name = "自动检测"; break;
        case VendorType::HIKVISION: vendor_name = "海康威视"; break;
        case VendorType::DAHUA: vendor_name = "大华"; break;
        case VendorType::GENERIC: vendor_name = "通用格式"; break;
    }

    std::cout << "输入文件: " << options.input_file << std::endl;
    std::cout << "输出文件: " << options.output_file << std::endl;
    std::cout << "SEI解析模式: " << vendor_name << std::endl;
    if (options.debug_mode) {
        std::cout << "调试模式: 已启用" << std::endl;
    }
    std::cout << std::endl;

    std::cout << "正在扫描视频时间戳..." << std::endl;

    int timestamp_count = 0;
    AVPacket pkt;

    while (av_read_frame(format_ctx, &pkt) >= 0) {
        if (pkt.stream_index == video_stream_index && pkt.data && pkt.size > 0) {
            uint8_t *data = pkt.data;
            size_t size = pkt.size;
            size_t pos = 0;

            while (pos < size) {
                // 查找NAL单元起始码
                if (pos + 3 < size && data[pos] == 0x00 && data[pos + 1] == 0x00 && data[pos + 2] == 0x01) {
                    uint8_t nal_type = data[pos + 3] & 0x1F;

                    if (nal_type == 6) { // SEI NALU
                        size_t sei_payload_start = pos + 4; // 跳过起始码和NAL头
                        size_t sei_payload_size = 0;

                        // 找到SEI数据的结束位置
                        while (sei_payload_start + sei_payload_size < size &&
                               data[sei_payload_start + sei_payload_size] != 0x80) {
                            sei_payload_size++;
                        }

                        if (sei_payload_start + sei_payload_size < size && sei_payload_size > 20) {
                            // 使用增强的SEI解析器
                            int64_t timestamp = parser.parse_sei_timestamp(
                                data + sei_payload_start,
                                sei_payload_size,
                                options.vendor
                            );

                            if (timestamp != -1) {
                                out_file << timestamp << std::endl;
                                timestamp_count++;

                                if (!options.debug_mode && timestamp_count % 100 == 0) {
                                    std::cout << "已提取 " << timestamp_count << " 个时间戳..." << std::endl;
                                }
                            }
                        }

                        // 跳过整个SEI数据块
                        pos = sei_payload_start + sei_payload_size + 1;
                        continue;
                    }
                }
                pos++;
            }
        }
        av_packet_unref(&pkt);
    }

    out_file.close();
    avformat_close_input(&format_ctx);

    std::cout << "扫描完成，共提取 " << timestamp_count << " 个时间戳" << std::endl;
    return timestamp_count > 0;
}

int main(int argc, char *argv[]) {
    // 解析命令行参数
    CliOptions options = parse_arguments(argc, argv);

    // 显示帮助信息
    if (options.help) {
        show_help(argv[0]);
        return 0;
    }

    // 检查必需的参数
    if (options.input_file.empty()) {
        std::cerr << "错误: 未指定输入文件" << std::endl;
        std::cerr << "使用 --help 查看使用说明" << std::endl;
        return -1;
    }

    // 如果未指定输出文件，则默认与输入文件同名，后缀为 .txt
    if (options.output_file.empty()) {
        size_t last_dot = options.input_file.find_last_of('.');
        if (last_dot != std::string::npos) {
            options.output_file = options.input_file.substr(0, last_dot) + ".txt";
        } else {
            options.output_file = options.input_file + ".txt";
        }
    }

    // 执行时间戳提取
    if (extract_sei_timestamps(options)) {
        std::cout << "SEI 时间戳已成功提取并保存到 " << options.output_file << std::endl;
        return 0;
    } else {
        std::cerr << "提取 SEI 时间戳失败" << std::endl;
        return -1;
    }
}
