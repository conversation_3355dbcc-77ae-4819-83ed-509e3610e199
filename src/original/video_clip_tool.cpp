#include <cstdint>
#include <cstring>
#include <cstdlib>
#include <fstream>
#include <iostream>
#include <stdexcept>
#include <string>
#include <vector>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <regex>
#include <ctime>

extern "C"
{
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/time.h>
#include <libavutil/opt.h>
}

// 厂商类型枚举
enum class VendorType {
    AUTO_DETECT = 0,
    DAHUA = 1,
    HIKVISION = 2,
    GENERIC = 3
};

// 厂商特定常量
struct VendorConstants {
    static const char* HIKVISION_UUID;
    static const char* DAHUA_UUID;
    static const size_t HIKVISION_PAYLOAD_SIZE = 0x21;  // 33字节
    static const size_t DAHUA_PAYLOAD_SIZE = 0x30;      // 48字节
    static const size_t TIMESTAMP_SIZE = 14;
    static const size_t UUID_SIZE = 16;
};

// 静态常量定义
const char* VendorConstants::HIKVISION_UUID = "HikTechnology"; // 支持多种变体
const char* VendorConstants::DAHUA_UUID = "DahuaTechnology";

// 命令行选项结构
struct CliOptions {
    std::string input_file;
    std::string output_file;
    std::string start_time_str;
    std::string end_time_str;
    VendorType vendor_type = VendorType::AUTO_DETECT;
    bool debug_mode = false;
    bool show_help = false;
};

// 时间戳信息结构
struct TimestampInfo {
    int64_t timestamp_ms;  // 毫秒时间戳
    int64_t dts;          // 解码时间戳
    int64_t pts;          // 显示时间戳
    int frame_index;      // 帧索引
    bool is_keyframe;     // 是否为关键帧
};

// 厂商感知的 SEI 解析器类
class VendorSEIParser {
private:
    VendorType vendor_type_;
    bool debug_mode_;

    // 厂商特定解析方法
    int64_t parse_hikvision_sei(uint8_t* data, size_t size);
    int64_t parse_dahua_sei(uint8_t* data, size_t size);
    int64_t parse_generic_sei(uint8_t* data, size_t size);

    // 工具方法
    VendorType detect_vendor_from_uuid(uint8_t* sei_data, size_t sei_size);
    void dump_sei_hex(uint8_t* data, size_t size, const std::string& label);
    bool validate_timestamp(int64_t timestamp);

public:
    VendorSEIParser(VendorType type = VendorType::AUTO_DETECT, bool debug = false)
        : vendor_type_(type), debug_mode_(debug) {}

    int64_t extract_timestamp(uint8_t* data, size_t size);
    void set_vendor_type(VendorType type) { vendor_type_ = type; }
    void set_debug_mode(bool debug) { debug_mode_ = debug; }
};

// 从 SEI 数据中提取时间戳（保持向后兼容）
int64_t extract_timestamp_from_sei(uint8_t* data, size_t size) {
    // 使用通用解析器保持向后兼容
    static VendorSEIParser parser(VendorType::GENERIC, false);
    return parser.extract_timestamp(data, size);
}

// VendorSEIParser 方法实现

// 验证时间戳是否合理
bool VendorSEIParser::validate_timestamp(int64_t timestamp) {
    // 验证是否为合理的毫秒时间戳（2000年后到2100年前）
    return timestamp > 946684800000LL && timestamp < 4102444800000LL;
}

// 十六进制转储SEI数据（调试用）
void VendorSEIParser::dump_sei_hex(uint8_t* data, size_t size, const std::string& label) {
    if (!debug_mode_) return;

    std::cout << "[DEBUG] " << label << " (" << size << " bytes):" << std::endl;
    for (size_t i = 0; i < size; i++) {
        if (i % 16 == 0) {
            std::cout << std::setfill('0') << std::setw(4) << std::hex << i << ": ";
        }
        std::cout << std::setfill('0') << std::setw(2) << std::hex << (int)data[i] << " ";
        if ((i + 1) % 16 == 0) {
            std::cout << std::endl;
        }
    }
    if (size % 16 != 0) {
        std::cout << std::endl;
    }
    std::cout << std::dec;  // 恢复十进制输出
}

// 从UUID检测厂商类型
VendorType VendorSEIParser::detect_vendor_from_uuid(uint8_t* sei_data, size_t sei_size) {
    if (sei_size < VendorConstants::UUID_SIZE) {
        return VendorType::GENERIC;
    }

    // 检查是否包含海康UUID（支持多种变体：HikTechnologyxy, HikTechnologyxx等）
    if (memcmp(sei_data, VendorConstants::HIKVISION_UUID, 13) == 0) {
        if (debug_mode_) {
            std::cout << "[DEBUG] 检测到海康威视SEI格式" << std::endl;
        }
        return VendorType::HIKVISION;
    }

    // 检查是否包含大华UUID
    if (memcmp(sei_data, VendorConstants::DAHUA_UUID, 15) == 0) {
        if (debug_mode_) {
            std::cout << "[DEBUG] 检测到大华SEI格式" << std::endl;
        }
        return VendorType::DAHUA;
    }

    if (debug_mode_) {
        std::cout << "[DEBUG] 未识别厂商，使用通用格式" << std::endl;
    }
    return VendorType::GENERIC;
}

// 海康威视SEI解析（增强版，支持多种格式变体）
int64_t VendorSEIParser::parse_hikvision_sei(uint8_t* data, size_t size) {
    size_t pos = 0;

    while (pos < size) {
        // 查找H.264起始码
        if (pos + 3 < size && data[pos] == 0x00 && data[pos + 1] == 0x00 && data[pos + 2] == 0x01) {
            uint8_t nal_type = data[pos + 3] & 0x1F;

            if (nal_type == 6) { // SEI NALU
                size_t sei_start = pos + 4;  // 跳过起始码和NAL头

                // 方法1：标准海康格式（单个负载类型字节）
                if (sei_start + 2 < size &&
                    data[sei_start] == 0x05 &&  // 负载类型
                    data[sei_start + 1] == VendorConstants::HIKVISION_PAYLOAD_SIZE) {

                    size_t uuid_start = sei_start + 2;

                    // 验证UUID（支持多种变体）
                    if (uuid_start + VendorConstants::UUID_SIZE <= size &&
                        memcmp(data + uuid_start, VendorConstants::HIKVISION_UUID, 13) == 0) { // 只比较前13个字符

                        size_t timestamp_start = uuid_start + VendorConstants::UUID_SIZE;

                        if (timestamp_start + VendorConstants::TIMESTAMP_SIZE <= size) {
                            char timestamp[VendorConstants::TIMESTAMP_SIZE + 1] = {0};
                            memcpy(timestamp, data + timestamp_start, VendorConstants::TIMESTAMP_SIZE);
                            timestamp[VendorConstants::TIMESTAMP_SIZE] = '\0';

                            if (debug_mode_) {
                                std::cout << "[DEBUG] 海康SEI时间戳字符串(标准格式): " << timestamp << std::endl;
                                dump_sei_hex(data + uuid_start, VendorConstants::HIKVISION_PAYLOAD_SIZE, "海康SEI负载(标准)");
                            }

                            try {
                                int64_t timestamp_ms = std::stoll(timestamp);
                                if (validate_timestamp(timestamp_ms)) {
                                    return timestamp_ms;
                                }
                            } catch (const std::exception &e) {
                                if (debug_mode_) {
                                    std::cerr << "[DEBUG] 海康时间戳解析失败: " << e.what() << std::endl;
                                }
                            }
                        }
                    }
                }

                // 方法2：变体格式（直接搜索海康UUID）
                for (size_t search_pos = sei_start; search_pos + VendorConstants::UUID_SIZE < size; search_pos++) {
                    if (memcmp(data + search_pos, VendorConstants::HIKVISION_UUID, 13) == 0) { // 只比较前13个字符
                        size_t timestamp_start = search_pos + VendorConstants::UUID_SIZE;

                        if (timestamp_start + VendorConstants::TIMESTAMP_SIZE <= size) {
                            char timestamp[VendorConstants::TIMESTAMP_SIZE + 1] = {0};
                            memcpy(timestamp, data + timestamp_start, VendorConstants::TIMESTAMP_SIZE);
                            timestamp[VendorConstants::TIMESTAMP_SIZE] = '\0';

                            if (debug_mode_) {
                                std::cout << "[DEBUG] 海康SEI时间戳字符串(变体格式): " << timestamp << std::endl;
                                dump_sei_hex(data + search_pos, std::min((size_t)40, size - search_pos), "海康SEI负载(变体)");
                            }

                            try {
                                int64_t timestamp_ms = std::stoll(timestamp);
                                if (validate_timestamp(timestamp_ms)) {
                                    return timestamp_ms;
                                }
                            } catch (const std::exception &e) {
                                if (debug_mode_) {
                                    std::cerr << "[DEBUG] 海康变体时间戳解析失败: " << e.what() << std::endl;
                                }
                            }
                        }
                        break; // 找到UUID后跳出搜索循环
                    }
                }

                // 跳过这个SEI块，寻找下一个
                size_t next_pos = sei_start;
                while (next_pos < size && data[next_pos] != 0x80) {
                    next_pos++;
                }
                pos = next_pos + 1;
                continue;
            }
        }
        pos++;
    }

    return -1;
}

// 大华SEI解析（增强版，支持多种格式变体）
int64_t VendorSEIParser::parse_dahua_sei(uint8_t* data, size_t size) {
    size_t pos = 0;

    while (pos < size) {
        // 查找H.264起始码
        if (pos + 3 < size && data[pos] == 0x00 && data[pos + 1] == 0x00 && data[pos + 2] == 0x01) {
            uint8_t nal_type = data[pos + 3] & 0x1F;

            if (nal_type == 6) { // SEI NALU
                size_t sei_start = pos + 4;  // 跳过起始码和NAL头

                // 方法1：标准大华格式（两个负载类型字节）
                if (sei_start + 3 < size &&
                    data[sei_start] == 0x05 &&      // 第一个负载类型
                    data[sei_start + 1] == 0x00 &&  // 第二个负载类型（大华特有）
                    data[sei_start + 2] == VendorConstants::DAHUA_PAYLOAD_SIZE) {

                    size_t uuid_start = sei_start + 3;

                    // 验证UUID
                    if (uuid_start + VendorConstants::UUID_SIZE <= size &&
                        memcmp(data + uuid_start, VendorConstants::DAHUA_UUID, 15) == 0) {

                        size_t timestamp_start = uuid_start + VendorConstants::UUID_SIZE;

                        if (timestamp_start + VendorConstants::TIMESTAMP_SIZE <= size) {
                            char timestamp[VendorConstants::TIMESTAMP_SIZE + 1] = {0};
                            memcpy(timestamp, data + timestamp_start, VendorConstants::TIMESTAMP_SIZE);
                            timestamp[VendorConstants::TIMESTAMP_SIZE] = '\0';

                            if (debug_mode_) {
                                std::cout << "[DEBUG] 大华SEI时间戳字符串(标准格式): " << timestamp << std::endl;
                                dump_sei_hex(data + uuid_start, VendorConstants::DAHUA_PAYLOAD_SIZE, "大华SEI负载(标准)");
                            }

                            try {
                                int64_t timestamp_ms = std::stoll(timestamp);
                                if (validate_timestamp(timestamp_ms)) {
                                    return timestamp_ms;
                                }
                            } catch (const std::exception &e) {
                                if (debug_mode_) {
                                    std::cerr << "[DEBUG] 大华时间戳解析失败: " << e.what() << std::endl;
                                }
                            }
                        }
                    }
                }

                // 方法2：变体格式（直接搜索大华UUID）
                for (size_t search_pos = sei_start; search_pos + VendorConstants::UUID_SIZE < size; search_pos++) {
                    if (memcmp(data + search_pos, VendorConstants::DAHUA_UUID, 15) == 0) {
                        size_t timestamp_start = search_pos + VendorConstants::UUID_SIZE;

                        if (timestamp_start + VendorConstants::TIMESTAMP_SIZE <= size) {
                            char timestamp[VendorConstants::TIMESTAMP_SIZE + 1] = {0};
                            memcpy(timestamp, data + timestamp_start, VendorConstants::TIMESTAMP_SIZE);
                            timestamp[VendorConstants::TIMESTAMP_SIZE] = '\0';

                            if (debug_mode_) {
                                std::cout << "[DEBUG] 大华SEI时间戳字符串(变体格式): " << timestamp << std::endl;
                                dump_sei_hex(data + search_pos, std::min((size_t)60, size - search_pos), "大华SEI负载(变体)");
                            }

                            try {
                                int64_t timestamp_ms = std::stoll(timestamp);
                                if (validate_timestamp(timestamp_ms)) {
                                    return timestamp_ms;
                                }
                            } catch (const std::exception &e) {
                                if (debug_mode_) {
                                    std::cerr << "[DEBUG] 大华变体时间戳解析失败: " << e.what() << std::endl;
                                }
                            }
                        }
                        break; // 找到UUID后跳出搜索循环
                    }
                }

                // 跳过这个SEI块，寻找下一个
                size_t next_pos = sei_start;
                while (next_pos < size && data[next_pos] != 0x80) {
                    next_pos++;
                }
                pos = next_pos + 1;
                continue;
            }
        }
        pos++;
    }

    return -1;
}

// 通用SEI解析（原有逻辑）
int64_t VendorSEIParser::parse_generic_sei(uint8_t* data, size_t size) {
    size_t pos = 0;

    while (pos < size) {
        if (pos + 3 < size && data[pos] == 0x00 && data[pos + 1] == 0x00 && data[pos + 2] == 0x01) {
            uint8_t nal_type = data[pos + 3] & 0x1F;
            size_t start_code_len = 3;

            if (nal_type == 6) { // SEI NALU
                size_t sei_payload_start = pos + start_code_len;
                size_t sei_payload_size = 0;

                // 找到 SEI 数据的结束位置
                while (sei_payload_start + sei_payload_size < size &&
                       data[sei_payload_start + sei_payload_size] != 0x80) {
                    sei_payload_size++;
                }

                if (sei_payload_start + sei_payload_size < size) {
                    if (debug_mode_) {
                        dump_sei_hex(data + sei_payload_start, sei_payload_size, "通用SEI数据");
                    }

                    // 尝试多个可能的时间戳偏移位置
                    const size_t possible_offsets[] = {19, 20, 18, 21, 17};
                    const size_t timestamp_size = 14;

                    for (size_t i = 0; i < sizeof(possible_offsets) / sizeof(possible_offsets[0]); i++) {
                        size_t timestamp_offset = possible_offsets[i];

                        if (sei_payload_start + timestamp_offset + timestamp_size <= size) {
                            char timestamp[timestamp_size + 1] = {0};
                            memcpy(timestamp, data + sei_payload_start + timestamp_offset, timestamp_size);
                            timestamp[timestamp_size] = '\0';

                            if (debug_mode_) {
                                std::cout << "[DEBUG] 通用SEI时间戳字符串(偏移" << timestamp_offset << "): " << timestamp << std::endl;
                            }

                            try {
                                int64_t timestamp_ms = std::stoll(timestamp);
                                if (validate_timestamp(timestamp_ms)) {
                                    return timestamp_ms;
                                }
                            } catch (const std::exception &e) {
                                if (debug_mode_) {
                                    std::cerr << "[DEBUG] 通用时间戳解析失败(偏移" << timestamp_offset << "): " << e.what() << std::endl;
                                }
                            }
                        }
                    }
                }

                // 跳过整个 SEI 数据块
                pos += start_code_len + sei_payload_size + 1;
                continue;
            }
        }
        pos++;
    }

    return -1;
}

// 主要的时间戳提取方法
int64_t VendorSEIParser::extract_timestamp(uint8_t* data, size_t size) {
    if (!data || size == 0) {
        return -1;
    }

    VendorType actual_vendor = vendor_type_;

    // 如果是自动检测模式，先尝试检测厂商
    if (vendor_type_ == VendorType::AUTO_DETECT) {
        // 快速扫描寻找SEI数据进行厂商检测
        size_t pos = 0;
        while (pos < size) {
            if (pos + 3 < size && data[pos] == 0x00 && data[pos + 1] == 0x00 && data[pos + 2] == 0x01) {
                uint8_t nal_type = data[pos + 3] & 0x1F;
                if (nal_type == 6) { // SEI NALU
                    size_t sei_start = pos + 4;
                    size_t remaining = size - sei_start;
                    if (remaining > VendorConstants::UUID_SIZE) {
                        // 尝试在不同偏移位置检测UUID
                        for (size_t offset = 0; offset < std::min(remaining - VendorConstants::UUID_SIZE, (size_t)10); offset++) {
                            VendorType detected = detect_vendor_from_uuid(data + sei_start + offset, remaining - offset);
                            if (detected != VendorType::GENERIC) {
                                actual_vendor = detected;
                                break;
                            }
                        }
                    }
                    break;
                }
            }
            pos++;
        }

        if (actual_vendor == VendorType::AUTO_DETECT) {
            actual_vendor = VendorType::GENERIC;
        }
    }

    if (debug_mode_) {
        std::string vendor_name;
        switch (actual_vendor) {
            case VendorType::HIKVISION: vendor_name = "海康威视"; break;
            case VendorType::DAHUA: vendor_name = "大华"; break;
            case VendorType::GENERIC: vendor_name = "通用"; break;
            default: vendor_name = "未知"; break;
        }
        std::cout << "[DEBUG] 使用 " << vendor_name << " SEI解析器" << std::endl;
    }

    // 根据厂商类型调用相应的解析方法
    int64_t result = -1;
    switch (actual_vendor) {
        case VendorType::HIKVISION:
            result = parse_hikvision_sei(data, size);
            break;
        case VendorType::DAHUA:
            result = parse_dahua_sei(data, size);
            break;
        case VendorType::GENERIC:
        default:
            result = parse_generic_sei(data, size);
            break;
    }

    // 如果特定厂商解析失败，尝试通用解析作为备选
    if (result == -1 && actual_vendor != VendorType::GENERIC) {
        if (debug_mode_) {
            std::cout << "[DEBUG] 厂商特定解析失败，尝试通用解析" << std::endl;
        }
        result = parse_generic_sei(data, size);
    }

    return result;
}

// 扫描视频文件，建立时间戳索引
bool scan_video_timestamps(const std::string& input_file, std::vector<TimestampInfo>& timestamps,
                          VendorSEIParser* sei_parser = nullptr) {
    AVFormatContext* format_ctx = nullptr;
    
    if (avformat_open_input(&format_ctx, input_file.c_str(), nullptr, nullptr) < 0) {
        std::cerr << "无法打开输入文件: " << input_file << std::endl;
        return false;
    }
    
    if (avformat_find_stream_info(format_ctx, nullptr) < 0) {
        std::cerr << "无法获取流信息: " << input_file << std::endl;
        avformat_close_input(&format_ctx);
        return false;
    }
    
    int video_stream_index = -1;
    for (unsigned i = 0; i < format_ctx->nb_streams; ++i) {
        if (format_ctx->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            video_stream_index = i;
            break;
        }
    }
    
    if (video_stream_index == -1) {
        std::cerr << "未找到视频流" << std::endl;
        avformat_close_input(&format_ctx);
        return false;
    }
    
    AVPacket pkt;
    int frame_index = 0;
    
    std::cout << "正在扫描视频时间戳..." << std::endl;
    
    while (av_read_frame(format_ctx, &pkt) >= 0) {
        if (pkt.stream_index == video_stream_index && pkt.data && pkt.size > 0) {
            TimestampInfo info;
            info.dts = pkt.dts;
            info.pts = pkt.pts;
            info.frame_index = frame_index;
            info.is_keyframe = (pkt.flags & AV_PKT_FLAG_KEY) != 0;
            
            // 提取 SEI 时间戳
            int64_t sei_timestamp = -1;
            if (sei_parser) {
                sei_timestamp = sei_parser->extract_timestamp(pkt.data, pkt.size);
            } else {
                sei_timestamp = extract_timestamp_from_sei(pkt.data, pkt.size);
            }

            if (sei_timestamp > 0) {
                info.timestamp_ms = sei_timestamp;
                timestamps.push_back(info);

                if (timestamps.size() % 100 == 0) {
                    std::cout << "已扫描 " << timestamps.size() << " 个时间戳帧..." << std::endl;
                }
            }
            
            frame_index++;
        }
        
        av_packet_unref(&pkt);
    }
    
    avformat_close_input(&format_ctx);
    
    std::cout << "扫描完成，共找到 " << timestamps.size() << " 个时间戳帧" << std::endl;
    
    return !timestamps.empty();
}

// 剪辑视频
bool clip_video(const std::string& input_file, const std::string& output_file,
                int64_t start_time_ms, int64_t end_time_ms,
                const std::vector<TimestampInfo>& timestamps) {
    
    // 找到开始和结束的帧
    auto start_it = std::lower_bound(timestamps.begin(), timestamps.end(), start_time_ms,
        [](const TimestampInfo& info, int64_t time) {
            return info.timestamp_ms < time;
        });
    
    auto end_it = std::upper_bound(timestamps.begin(), timestamps.end(), end_time_ms,
        [](int64_t time, const TimestampInfo& info) {
            return time < info.timestamp_ms;
        });
    
    if (start_it == timestamps.end() || end_it == timestamps.begin()) {
        std::cerr << "指定的时间范围内没有数据" << std::endl;
        return false;
    }
    
    // 向前查找最近的关键帧
    while (start_it != timestamps.begin() && !start_it->is_keyframe) {
        --start_it;
    }
    
    int64_t start_dts = start_it->dts;
    int64_t end_dts = (end_it != timestamps.end()) ? end_it->dts : INT64_MAX;
    
    std::cout << "剪辑时间范围: " << start_time_ms << "ms - " << end_time_ms << "ms" << std::endl;
    std::cout << "实际时间范围: " << start_it->timestamp_ms << "ms - " 
              << ((end_it != timestamps.end()) ? std::to_string(end_it->timestamp_ms) : "结束") << "ms" << std::endl;
    
    // 打开输入文件
    AVFormatContext* input_ctx = nullptr;
    if (avformat_open_input(&input_ctx, input_file.c_str(), nullptr, nullptr) < 0) {
        std::cerr << "无法打开输入文件: " << input_file << std::endl;
        return false;
    }
    
    if (avformat_find_stream_info(input_ctx, nullptr) < 0) {
        std::cerr << "无法获取流信息" << std::endl;
        avformat_close_input(&input_ctx);
        return false;
    }
    
    // 创建输出文件
    AVFormatContext* output_ctx = nullptr;
    if (avformat_alloc_output_context2(&output_ctx, nullptr, nullptr, output_file.c_str()) < 0) {
        std::cerr << "无法创建输出上下文" << std::endl;
        avformat_close_input(&input_ctx);
        return false;
    }
    
    // 复制流信息
    for (unsigned i = 0; i < input_ctx->nb_streams; i++) {
        AVStream* in_stream = input_ctx->streams[i];
        AVStream* out_stream = avformat_new_stream(output_ctx, nullptr);
        
        if (!out_stream) {
            std::cerr << "无法创建输出流" << std::endl;
            avformat_close_input(&input_ctx);
            avformat_free_context(output_ctx);
            return false;
        }
        
        if (avcodec_parameters_copy(out_stream->codecpar, in_stream->codecpar) < 0) {
            std::cerr << "无法复制编解码器参数" << std::endl;
            avformat_close_input(&input_ctx);
            avformat_free_context(output_ctx);
            return false;
        }
        
        out_stream->codecpar->codec_tag = 0;
    }
    
    // 打开输出文件
    if (!(output_ctx->oformat->flags & AVFMT_NOFILE)) {
        if (avio_open(&output_ctx->pb, output_file.c_str(), AVIO_FLAG_WRITE) < 0) {
            std::cerr << "无法打开输出文件: " << output_file << std::endl;
            avformat_close_input(&input_ctx);
            avformat_free_context(output_ctx);
            return false;
        }
    }
    
    // 写入文件头
    if (avformat_write_header(output_ctx, nullptr) < 0) {
        std::cerr << "无法写入文件头" << std::endl;
        avformat_close_input(&input_ctx);
        if (!(output_ctx->oformat->flags & AVFMT_NOFILE)) {
            avio_closep(&output_ctx->pb);
        }
        avformat_free_context(output_ctx);
        return false;
    }
    
    // 复制数据包
    AVPacket pkt;
    int64_t first_dts = -1;
    int64_t dts_offset = 0;
    int packet_count = 0;
    
    std::cout << "开始剪辑视频..." << std::endl;
    
    while (av_read_frame(input_ctx, &pkt) >= 0) {
        AVStream* in_stream = input_ctx->streams[pkt.stream_index];
        AVStream* out_stream = output_ctx->streams[pkt.stream_index];
        
        // 只处理指定时间范围内的数据包
        if (in_stream->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            if (pkt.dts < start_dts) {
                av_packet_unref(&pkt);
                continue;
            }
            
            if (pkt.dts > end_dts) {
                av_packet_unref(&pkt);
                break;
            }
            
            // 记录第一个 DTS 作为偏移基准
            if (first_dts == -1) {
                first_dts = pkt.dts;
                dts_offset = first_dts;
            }
        }
        
        // 调整时间戳
        if (pkt.pts != AV_NOPTS_VALUE) {
            pkt.pts -= dts_offset;
        }
        if (pkt.dts != AV_NOPTS_VALUE) {
            pkt.dts -= dts_offset;
        }
        
        // 重新缩放时间戳
        av_packet_rescale_ts(&pkt, in_stream->time_base, out_stream->time_base);
        pkt.pos = -1;
        
        // 写入数据包
        if (av_interleaved_write_frame(output_ctx, &pkt) < 0) {
            std::cerr << "写入数据包失败" << std::endl;
            av_packet_unref(&pkt);
            break;
        }
        
        packet_count++;
        if (packet_count % 100 == 0) {
            std::cout << "已处理 " << packet_count << " 个数据包..." << std::endl;
        }
        
        av_packet_unref(&pkt);
    }
    
    // 写入文件尾
    av_write_trailer(output_ctx);
    
    // 清理资源
    avformat_close_input(&input_ctx);
    if (!(output_ctx->oformat->flags & AVFMT_NOFILE)) {
        avio_closep(&output_ctx->pb);
    }
    avformat_free_context(output_ctx);
    
    std::cout << "剪辑完成，共处理 " << packet_count << " 个数据包" << std::endl;
    
    return true;
}

// 解析时间字符串（支持多种格式）
int64_t parse_time_string(const std::string& time_str) {
    // 支持以下格式：
    // 1. 纯毫秒数: "1723169047372"
    // 2. ISO格式: "2024-08-09T10:30:47.372" 或 "2024-08-09T10:30:47"
    // 3. 简单格式: "10:30:47.372"
    
    // 尝试解析为纯数字（毫秒时间戳）
    try {
        int64_t timestamp = std::stoll(time_str);
        // 验证是否为合理的毫秒时间戳（2000年后到2100年前）
        if (timestamp > 946684800000LL && timestamp < 4102444800000LL) {
            return timestamp;
        }
    } catch (...) {
        // 不是纯数字，尝试其他格式
    }
    
    // 尝试解析 ISO 格式: YYYY-MM-DDTHH:MM:SS[.mmm][Z|+HH:MM|-HH:MM]
    // 支持多种ISO格式变体
    std::regex iso_regex(R"((\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{1,3}))?(?:Z|([+-]\d{2}):?(\d{2}))?)");;
    std::smatch iso_match;
    
    if (std::regex_match(time_str, iso_match, iso_regex)) {
        // 解析日期时间部分
        int year = std::stoi(iso_match[1]);
        int month = std::stoi(iso_match[2]);
        int day = std::stoi(iso_match[3]);
        int hour = std::stoi(iso_match[4]);
        int minute = std::stoi(iso_match[5]);
        int second = std::stoi(iso_match[6]);
        
        // 获取毫秒部分
        int milliseconds = 0;
        if (iso_match[7].matched) {
            std::string ms_str = iso_match[7].str();
            // 补齐到3位
            while (ms_str.length() < 3) ms_str += "0";
            milliseconds = std::stoi(ms_str.substr(0, 3));
        }
        
        // 处理时区
        int tz_offset_hours = 8;  // 默认UTC+8（中国时区）
        int tz_offset_minutes = 0;
        
        if (iso_match[8].matched) {
            // 有明确的时区信息
            tz_offset_hours = std::stoi(iso_match[8]);
            if (iso_match[9].matched) {
                tz_offset_minutes = std::stoi(iso_match[9]);
                if (tz_offset_hours < 0) {
                    tz_offset_minutes = -tz_offset_minutes;
                }
            }
        } else if (time_str.find('Z') != std::string::npos) {
            // UTC时间
            tz_offset_hours = 0;
            tz_offset_minutes = 0;
        }
        
        // 方法1：使用系统调用转换（更可靠）
        std::tm tm = {};
        tm.tm_year = year - 1900;
        tm.tm_mon = month - 1;
        tm.tm_mday = day;
        tm.tm_hour = hour;
        tm.tm_min = minute;
        tm.tm_sec = second;
        tm.tm_isdst = 0;
        
        // 临时设置时区为UTC进行计算
        char* old_tz = getenv("TZ");
        std::string saved_tz = old_tz ? old_tz : "";
        setenv("TZ", "UTC", 1);
        tzset();
        
        time_t utc_time = std::mktime(&tm);
        
        // 恢复时区
        if (!saved_tz.empty()) {
            setenv("TZ", saved_tz.c_str(), 1);
        } else {
            unsetenv("TZ");
        }
        tzset();
        
        if (utc_time == -1) {
            // 方法2：手动计算（备用方案）
            // 简化的日期转换，假设没有闰秒
            int64_t days = 0;
            
            // 计算从1970年1月1日到指定日期的天数
            for (int y = 1970; y < year; y++) {
                days += (y % 4 == 0 && (y % 100 != 0 || y % 400 == 0)) ? 366 : 365;
            }
            
            int month_days[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
            if (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)) {
                month_days[1] = 29;  // 闰年2月
            }
            
            for (int m = 1; m < month; m++) {
                days += month_days[m - 1];
            }
            days += day - 1;
            
            // 转换为秒，然后调整时区
            int64_t total_seconds = days * 86400LL + hour * 3600LL + minute * 60LL + second;
            total_seconds -= (tz_offset_hours * 3600 + tz_offset_minutes * 60);
            
            return total_seconds * 1000LL + milliseconds;
        }
        
        // 调整时区偏移
        utc_time -= (tz_offset_hours * 3600 + tz_offset_minutes * 60);
        
        int64_t result = static_cast<int64_t>(utc_time) * 1000 + milliseconds;
        
        // 验证结果是否合理
        if (result < 946684800000LL || result > 4102444800000LL) {
            std::cerr << "[WARNING] ISO时间解析结果异常: " << result << " ms，尝试备用方案" << std::endl;
            
            // 备用方案：使用系统命令date（仅Linux/macOS）
            std::string cmd = "date -d '" + time_str + "' +%s%3N 2>/dev/null";
            FILE* pipe = popen(cmd.c_str(), "r");
            if (pipe) {
                char buffer[128];
                if (fgets(buffer, sizeof(buffer), pipe)) {
                    pclose(pipe);
                    try {
                        result = std::stoll(buffer);
                        std::cerr << "[INFO] 使用系统date命令解析成功: " << result << " ms" << std::endl;
                        return result;
                    } catch (...) {}
                }
                pclose(pipe);
            }
        }
        
        return result;
    }
    
    // 尝试简单时间格式 HH:MM:SS[.mmm]
    std::regex time_regex(R"((\d{1,2}):(\d{2}):(\d{2})(?:\.(\d{3}))?)");;
    std::smatch time_match;
    
    if (std::regex_match(time_str, time_match, time_regex)) {
        int hours = std::stoi(time_match[1]);
        int minutes = std::stoi(time_match[2]);
        int seconds = std::stoi(time_match[3]);
        int milliseconds = 0;
        
        if (time_match[4].matched) {
            milliseconds = std::stoi(time_match[4]);
        }
        
        // 转换为相对毫秒数
        return (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds;
    }
    
    std::cerr << "无法解析时间字符串: " << time_str << std::endl;
    std::cerr << "支持的格式：" << std::endl;
    std::cerr << "  1. 毫秒时间戳: 1723169047372" << std::endl;
    std::cerr << "  2. ISO格式: 2024-08-09T10:30:47.372" << std::endl;
    std::cerr << "  3. 时分秒格式: 10:30:47.372" << std::endl;
    return -1;
}

// 字符串转换为厂商类型
VendorType string_to_vendor_type(const std::string& vendor_str) {
    std::string lower_str = vendor_str;
    std::transform(lower_str.begin(), lower_str.end(), lower_str.begin(), ::tolower);

    if (lower_str == "hikvision" || lower_str == "hik" || lower_str == "海康") {
        return VendorType::HIKVISION;
    } else if (lower_str == "dahua" || lower_str == "dh" || lower_str == "大华") {
        return VendorType::DAHUA;
    } else if (lower_str == "generic" || lower_str == "通用") {
        return VendorType::GENERIC;
    } else if (lower_str == "auto" || lower_str == "自动") {
        return VendorType::AUTO_DETECT;
    }

    return VendorType::AUTO_DETECT;  // 默认自动检测
}

// 解析命令行参数
CliOptions parse_command_line(int argc, char* argv[]) {
    CliOptions options;

    if (argc < 5) {
        options.show_help = true;
        return options;
    }

    // 解析基本参数
    options.input_file = argv[1];
    options.output_file = argv[2];
    options.start_time_str = argv[3];
    options.end_time_str = argv[4];

    // 解析可选参数
    for (int i = 5; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--vendor" && i + 1 < argc) {
            options.vendor_type = string_to_vendor_type(argv[++i]);
        } else if (arg == "--debug") {
            options.debug_mode = true;
        } else if (arg == "--help" || arg == "-h") {
            options.show_help = true;
        }
    }

    return options;
}

void print_usage(const char* program_name) {
    std::cout << "用法: " << program_name << " <输入视频文件> <输出视频文件> <开始时间> <结束时间> [选项]" << std::endl;
    std::cout << std::endl;
    std::cout << "时间格式支持：" << std::endl;
    std::cout << "  1. 毫秒时间戳: 1723169047372" << std::endl;
    std::cout << "  2. ISO格式: 2024-08-09T10:30:47.372" << std::endl;
    std::cout << "  3. 时分秒格式: 10:30:47.372" << std::endl;
    std::cout << std::endl;
    std::cout << "选项：" << std::endl;
    std::cout << "  --vendor <type>     指定厂商类型 (hikvision|dahua|generic|auto) [默认: auto]" << std::endl;
    std::cout << "  --debug            启用调试模式，显示详细的SEI解析信息" << std::endl;
    std::cout << "  --help, -h         显示此帮助信息" << std::endl;
    std::cout << std::endl;
    std::cout << "厂商类型说明：" << std::endl;
    std::cout << "  hikvision (hik)    海康威视SEI格式" << std::endl;
    std::cout << "  dahua (dh)         大华SEI格式" << std::endl;
    std::cout << "  generic            通用SEI格式" << std::endl;
    std::cout << "  auto               自动检测厂商类型" << std::endl;
    std::cout << std::endl;
    std::cout << "示例：" << std::endl;
    std::cout << "  " << program_name << " input.ts output.ts 1723169047372 1723169050000" << std::endl;
    std::cout << "  " << program_name << " input.ts output.ts \"2024-08-09T10:30:47\" \"2024-08-09T10:30:50\" --vendor hikvision" << std::endl;
    std::cout << "  " << program_name << " input.ts output.ts start_time end_time --vendor dahua --debug" << std::endl;
}

int main(int argc, char* argv[]) {
    // 解析命令行参数
    CliOptions options = parse_command_line(argc, argv);

    if (options.show_help || argc < 5) {
        print_usage(argv[0]);
        return options.show_help ? 0 : -1;
    }

    std::string input_file = options.input_file;
    std::string output_file = options.output_file;
    std::string start_time_str = options.start_time_str;
    std::string end_time_str = options.end_time_str;
    
    // 解析时间参数
    int64_t start_time_ms = parse_time_string(start_time_str);
    int64_t end_time_ms = parse_time_string(end_time_str);
    
    if (start_time_ms < 0 || end_time_ms < 0) {
        std::cerr << "时间参数解析失败" << std::endl;
        std::cerr << "开始时间字符串: " << start_time_str << " -> " << start_time_ms << std::endl;
        std::cerr << "结束时间字符串: " << end_time_str << " -> " << end_time_ms << std::endl;
        
        // 如果解析失败，尝试使用系统date命令作为备用方案
        std::cerr << "尝试使用系统date命令解析..." << std::endl;
        
        std::string cmd1 = "date -d '" + start_time_str + "' +%s%3N 2>/dev/null";
        std::string cmd2 = "date -d '" + end_time_str + "' +%s%3N 2>/dev/null";
        
        FILE* pipe1 = popen(cmd1.c_str(), "r");
        FILE* pipe2 = popen(cmd2.c_str(), "r");
        
        if (pipe1 && pipe2) {
            char buffer1[128], buffer2[128];
            if (fgets(buffer1, sizeof(buffer1), pipe1) && fgets(buffer2, sizeof(buffer2), pipe2)) {
                try {
                    start_time_ms = std::stoll(buffer1);
                    end_time_ms = std::stoll(buffer2);
                    std::cerr << "系统date命令解析成功:" << std::endl;
                    std::cerr << "  开始时间: " << start_time_ms << " ms" << std::endl;
                    std::cerr << "  结束时间: " << end_time_ms << " ms" << std::endl;
                } catch (...) {
                    std::cerr << "系统date命令解析失败" << std::endl;
                }
            }
            if (pipe1) pclose(pipe1);
            if (pipe2) pclose(pipe2);
        }
        
        // 再次检查
        if (start_time_ms < 0 || end_time_ms < 0) {
            return -1;
        }
    }
    
    if (start_time_ms >= end_time_ms) {
        std::cerr << "开始时间必须小于结束时间" << std::endl;
        std::cerr << "开始时间: " << start_time_ms << " ms (" << start_time_str << ")" << std::endl;
        std::cerr << "结束时间: " << end_time_ms << " ms (" << end_time_str << ")" << std::endl;
        std::cerr << "时间差: " << (end_time_ms - start_time_ms) << " ms" << std::endl;
        return -1;
    }
    
    std::cout << "输入文件: " << input_file << std::endl;
    std::cout << "输出文件: " << output_file << std::endl;
    std::cout << "开始时间: " << start_time_ms << " ms" << std::endl;
    std::cout << "结束时间: " << end_time_ms << " ms" << std::endl;
    std::cout << "剪辑时长: " << (end_time_ms - start_time_ms) / 1000.0 << " 秒" << std::endl;

    // 显示厂商类型信息
    std::string vendor_name;
    switch (options.vendor_type) {
        case VendorType::HIKVISION: vendor_name = "海康威视"; break;
        case VendorType::DAHUA: vendor_name = "大华"; break;
        case VendorType::GENERIC: vendor_name = "通用"; break;
        case VendorType::AUTO_DETECT: vendor_name = "自动检测"; break;
    }
    std::cout << "SEI解析模式: " << vendor_name << std::endl;
    if (options.debug_mode) {
        std::cout << "调试模式: 已启用" << std::endl;
    }
    std::cout << std::endl;

    // 创建SEI解析器
    VendorSEIParser sei_parser(options.vendor_type, options.debug_mode);

    // 扫描视频时间戳
    std::vector<TimestampInfo> timestamps;
    if (!scan_video_timestamps(input_file, timestamps, &sei_parser)) {
        std::cerr << "扫描视频时间戳失败" << std::endl;
        return -1;
    }
    
    // 显示时间戳范围
    if (!timestamps.empty()) {
        std::cout << "视频时间戳范围: " << timestamps.front().timestamp_ms 
                  << " ms - " << timestamps.back().timestamp_ms << " ms" << std::endl;
        std::cout << std::endl;
    }
    
    // 执行剪辑
    if (clip_video(input_file, output_file, start_time_ms, end_time_ms, timestamps)) {
        std::cout << "视频剪辑成功！输出文件: " << output_file << std::endl;
        return 0;
    } else {
        std::cerr << "视频剪辑失败" << std::endl;
        return -1;
    }
}
