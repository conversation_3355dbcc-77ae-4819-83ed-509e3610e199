
title = "GatherTools Config File"

version = 2.0

# 文件保存路径
path = "save"

[[camera]]
# version_sensor支持：ArchiveVisionV1, ArchiveDHVisionV1, ArchiveHKVisionV2
# 分别代表通用：通用版，大华相机带ntp时间戳版，海康相机带SEI帧时间戳版
# ArchiveDHVisionV1可以在Windows(x86_64)、Linux(x86_64,aarch64)上运行
# ArchiveDHVisionV1仅支持Linux(x86_64,aarch64)上运行
# ArchiveHKVisionV1仅支持Linux(x86_64,aarch64)上运行
version_sensor = "ArchiveHKVisionV2"
ip = "************"
port = 8554
id = "root"
password = "root"
ext = "11"
encoding = "h264"
# 相机底层传输通讯协议：0：自动选择，1：UDP，2：UDP-MCast，3：TCP，4：HTTP，5：TLS
# 当采集回的视频出现拖影、画面糊掉等情况时，使用TCP可以缓解上述问题，但同时会引入80ms左右的延时
protocol = 3

[[camera]]
version_sensor = "ArchiveHKVisionV2"
ip = "************"
port = 8554
id = "root"
password = "root"
ext = "21"
encoding = "h264"
protocol = 3

[[camera]]
version_sensor = "ArchiveHKVisionV2"
ip = "************"
port = 8554
id = "root"
password = "root"
ext = "31"
encoding = "h264"
protocol = 3

[[camera]]
version_sensor = "ArchiveHKVisionV2"
ip = "************"
port = 8554
id = "root"
password = "root"
ext = "41"
encoding = "h264"
protocol = 3



#[[radar]]
#ip = "************"
#port = 50801
#sn = ""
